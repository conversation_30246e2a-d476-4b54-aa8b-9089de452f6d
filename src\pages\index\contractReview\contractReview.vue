<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="contract-review-page">
    <CustomNavbar title="合同审查" :show-back="true" :transparent="false" />
    <view class="contract-review-container" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="contract-info">
        <view class="file-item">
          <view class="file-icon">
            <image
              v-if="contractInfo.isDocFile"
              src="@/static/images/docx-icon.svg"
              class="pdf-icon"
            ></image>
            <image v-else src="@/static/images/pdf-icon.svg" class="pdf-icon"></image>
          </view>
          <text class="file-name">{{ fileName }}</text>
        </view>
      </view>
      <view class="detail-container">
        <view class="detail-header-container">
          <view class="header-indicator"></view>
          <text class="detail-header">选择你的立场</text>
        </view>
        <view>
          <text class="detail-subheader">选定你的合同审查立场</text>
        </view>

        <view class="stance-section">
          <view
            class="stance-item"
            :class="{ active: selectedStance === 'buyer' }"
            @click="selectStance('buyer')"
          >
            <view class="stance-content">
              <text class="stance-label">订购方</text>
              <text class="stance-company">南京喵意信息科技有限公司</text>
            </view>
          </view>

          <view
            class="stance-item"
            :class="{ active: selectedStance === 'supplier' }"
            @click="selectStance('supplier')"
          >
            <view class="stance-content">
              <text class="stance-label">供应方</text>
              <text class="stance-company">南京古画广告制作有限公司</text>
            </view>
          </view>
        </view>

        <view class="detail-header-container">
          <view class="header-indicator"></view>
          <text class="detail-header">选择你的审查尺度</text>
        </view>
        <view>
          <text class="detail-subheader">选定不同审查尺度，对应不同的审查范围</text>
        </view>

        <view class="review-level-section">
          <view
            class="level-item"
            v-for="level in reviewLevels"
            :key="level.value"
            @click="selectReviewLevel(level.value)"
            :class="{ 'level-active': selectedReviewLevel === level.value }"
          >
            <text class="level-text">{{ level.label }}</text>
          </view>
        </view>
        <text class="review-description">
          {{ reviewLevels[selectedReviewLevel].tips }}
        </text>

        <view class="detail-header-container">
          <view class="header-indicator"></view>
          <text class="detail-header">审查清单</text>
        </view>
        <view>
          <text class="detail-subheader">系统提供的审查清单，由AI自动匹配</text>
        </view>
        <wd-cell
          title=""
          label="请选择审查清单"
          is-link
          custom-style="border: 1px solid #eaebf0; border-radius: 12rpx;margin-top: 24rpx"
        ></wd-cell>
      </view>

      <!-- 开始审查按钮 -->
      <view class="button-container">
        <button class="start-review-btn" @click="startReview">
          <view>
            <image
              style="width: 40rpx; height: 40rpx; margin-right: 8rpx"
              src="@/static/images/ai-star.svg"
            />
            <text>开始审查</text>
          </view>
        </button>
      </view>
    </view>

    <wd-popup
      v-model="showContractTypePicker"
      position="bottom"
      custom-style="border-radius: 24rpx 24rpx 0 0;"
    >
      <i-picker
        title="合同类型"
        :options="contractTypeOptions"
        :value="contractTypeValue"
        @cancel="showContractTypePicker = false"
        @confirm="handleContractTypeConfirm"
      />
    </wd-popup>
    <wd-popup
      v-model="showReviewStancePicker"
      position="bottom"
      custom-style="border-radius: 24rpx 24rpx 0 0;"
    >
      <i-picker
        title="审查立场"
        :options="stanceOptions"
        :value="stanceValue"
        @cancel="showReviewStancePicker = false"
        @confirm="handleReviewStanceConfirm"
      />
    </wd-popup>

    <wd-popup
      v-model="backConfirmVisible"
      custom-style="border-radius:32rpx; width: 640rpx; height: 320rpx;"
    >
      <view class="popup-content-container">
        <text class="confirm-text">若返回，已上传合同不会保存</text>
        <text class="confirm-text">确定要返回吗</text>
        <view class="popup-content-footer">
          <view class="cancel-button action-button" @click="backConfirmVisible = false">取消</view>
          <view class="divider"></view>
          <view class="confirm-button action-button" @click="backToHome">确定</view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomNavbar from '@/components/CustomNavbar.vue'
import IPicker from '@/components/IPicker.vue'
import {
  getRuleTypePositionList,
  initiateReview,
  queryRuleName,
  queryReviewRuleTree,
  queryContractInfoByContractId,
} from '@/service/index/effective'
import { useReviewStore } from '@/store'
import { ContractInfo } from '@/utils/help'
import { showToast, showLoading, hideLoading } from '@/utils/uni-api'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const reviewStore = useReviewStore()

const contractInfo = ref<ContractInfo>(new ContractInfo())

const fileName = ref('')
const fileType = ref('')
const checkDefaultKeys = ref([])

const backConfirmVisible = ref(false)

// 合同类型相关
const contractType = ref('')
const contractTypeValue = ref('')
const showContractTypePicker = ref(false)
const contractTypeOptions = ref([])

// 审查立场相关
const reviewStance = ref('')
const stanceValue = ref('')
const showReviewStancePicker = ref(false)
const stanceOptions = ref([])

// 立场选择相关
const selectedStance = ref('')
const selectedReviewLevel = ref('1')

const reviewLevels = ref([
  {
    label: '强势',
    value: '0',
    tips: '你所代表阵营的谈判地位较高，对合同的修改尺度较大，可修改的程度较高，系统在审查修改时会尽量多争取权利条款，尽量避免义务条款。',
  },

  {
    label: '中立',
    value: '1',
    tips: '合同双方欲对判地位势均，你所代表的阵营无明显优势，系统在在审查合同时以立完善合同内容、增强合同的可操作性为出发点，以权利义务对等为原则，对合同内容进行审查和检验。',
  },
  {
    label: '弱势',
    value: '2',
    tips: '你所代表阵营的谈判地位较低，对合同的修改尺度处于相对务势。系统在审查修改时对于与实现合同核心目的有关的底线问题必须修改，对于非底线问题可适度放宽审查标准。',
  },
])

const selectStance = (stance: string) => {
  selectedStance.value = stance
}

const selectReviewLevel = (level: string) => {
  selectedReviewLevel.value = level
}

const handleContractTypeConfirm = (item) => {
  contractType.value = item.label
  contractTypeValue.value = item.value
  reviewStance.value = ''
  stanceValue.value = ''
  stanceOptions.value = []
  showContractTypePicker.value = false
  contractTypeOptions.value.forEach((option) => {
    if (option.value === item.value) {
      option.itermList.forEach((option) => {
        stanceOptions.value.push({
          label: option.rulePosition,
          value: option.ruleCode,
        })
      })
    }
  })
}

const handleReviewStanceConfirm = async (item) => {
  reviewStance.value = item.label
  stanceValue.value = item.value
  const res = await queryReviewRuleTree(stanceValue.value)
  if (res.code === RESPONSE_CODE_SUCCESS && Array.isArray(res.data)) {
    const tree = res.data.map((item) => ({
      id: item.id,
      label: item.ruleTypeName,
      children: item.child.map((node) => ({ id: node.id, label: node.ruleTypeName })),
    }))
    tree.forEach((item) => {
      item.children.forEach((node) => {
        checkDefaultKeys.value.push(node.id)
      })
    })
  } else {
    showToast('获取审查立场树失败', 'error')
    console.error('获取审查立场树失败', res)
    return
  }
  showReviewStancePicker.value = false
}

const chooseReviewStance = () => {
  if (!contractType.value) {
    showToast('请先选择合同类型', 'error')
    return
  }
  showReviewStancePicker.value = true
}

const navBarHeight = ref(0)
const statusBarHeight = ref(0)
const safeAreaInsetsTop = ref(0)

// 页面加载
onLoad(async (options) => {
  const contractId = options.contractId
  showLoading()
  const contractInfoRes = await queryContractInfoByContractId(contractId)
  if (contractInfoRes.code !== RESPONSE_CODE_SUCCESS) {
    showToast('获取合同信息失败', 'error')
    hideLoading()
    return
  } else {
    const isDocFile =
      (contractInfoRes.data as any).contractName.endsWith('.doc') ||
      (contractInfoRes.data as any).contractName.endsWith('.docx')
    contractInfo.value = new ContractInfo()
      .setContractName((contractInfoRes.data as any).contractName)
      .setContractUrl((contractInfoRes.data as any).originalFileCode)
      .setContractId(options.contractId)
      .setIsDoc(isDocFile)
    if (contractInfo.value.contractName) {
      fileName.value = contractInfo.value.contractName.split('.')[0]
      fileType.value = contractInfo.value.contractName.split('.')[1]
    }
  }

  const res = await getRuleTypePositionList()
  if (res.code === RESPONSE_CODE_SUCCESS && Array.isArray(res.data)) {
    contractTypeOptions.value = res.data.map((item) => ({
      label: item.typeName,
      value: item.typeName,
      itermList: item.itermList,
    }))
  } else {
    console.error('获取审查立场列表失败', res)
    return
  }
  const { data: ruleName } = await queryRuleName({ fileCode: contractInfo.value.contractUrl })
  if (contractTypeOptions.value.length > 0) {
    contractTypeOptions.value.forEach((item) => {
      if (item.value === ruleName) {
        contractType.value = item.label
        contractTypeValue.value = item.value
        reviewStance.value = ''
        stanceValue.value = ''
        item.itermList.forEach((option) => {
          stanceOptions.value.push({
            label: option.rulePosition,
            value: option.ruleCode,
          })
        })
      }
    })
  }
  hideLoading()
})

// 获取系统信息
onMounted(() => {
  // 从基础库 2.20.1 开始，本接口停止维护，请使用 wx.getSystemSetting、wx.getAppAuthorizeSetting、wx.getDeviceInfo、wx.getWindowInfo、wx.getAppBaseInfo 代替
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  navBarHeight.value = 44
  safeAreaInsetsTop.value = statusBarHeight.value + navBarHeight.value
})

onBeforeUnmount(() => {
  reviewStore.removeContractInfo()
})

const backToHome = () => {
  setTimeout(() => {
    uni.navigateBack({
      delta: 1,
    })
  }, 0)
}

// 开始审查
const startReview = async () => {
  if (contractTypeValue.value === '') {
    showToast('请选择合同类型', 'error')
    return
  }
  if (stanceValue.value === '') {
    showToast('请选择审查立场', 'error')
    return
  }

  // 请求订阅消息
  try {
    await uni.requestSubscribeMessage({
      tmplIds: ['your_template_id'], // 替换为你的模板ID
      success: (res) => {
        console.log('订阅消息授权结果:', res)
      },
      fail: (err) => {
        console.log('订阅消息授权失败:', err)
      },
    })
  } catch (error) {
    console.log('订阅消息请求异常:', error)
  }

  const res = await initiateReview({
    sourceType: 'ITERMS',
    fileCode: contractInfo.value.contractUrl,
    contractId: contractInfo.value.contractId,
    contractName: contractInfo.value.contractName,
    llmReviewRuleListCode: stanceValue.value,
    llmReviewRuleIdList: checkDefaultKeys.value,
  })
  if (res.code === RESPONSE_CODE_SUCCESS) {
    const reviewId = res.data

    if (!contractInfo.value.reviewId && reviewId) {
      contractInfo.value.reviewId = reviewId as string
      uni.navigateTo({
        url: '/pages/index/contractReview/reviewResult?contractId=' + contractInfo.value.contractId,
      })
    }
  } else {
    showToast('发起审查失败，请稍后再试', 'error')
  }
}
</script>

<style lang="scss" scoped>
.contract-review-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9f9fb;
}
.contract-review-container {
  flex: 1;
  padding-bottom: 0;
  overflow-y: auto;
}
// 合同文件信息
.contract-info {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background-color: #fff;

  .file-item {
    display: flex;
    align-items: center;

    .file-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      border-radius: 16rpx;
      .pdf-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .file-name {
      margin-left: 8rpx;
      overflow: hidden;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.detail-container {
  padding: 24rpx;
  margin: 32rpx 32rpx 200rpx 32rpx; /* 底部增加200rpx边距 */
  background: #fff;
  border-radius: 16rpx;
  .detail-header-container {
    display: flex;
    align-items: center;
    .header-indicator {
      width: 8rpx;
      height: 32rpx;
      margin-right: 16rpx;
      background-color: #773bef;
    }
    .detail-header {
      font-size: 32rpx;
      font-weight: 600;
      color: #221d39;
    }
  }

  .detail-subheader {
    margin-left: 24rpx;
    font-size: 24rpx;
    color: #a2a1a9;
  }

  .stance-section {
    margin-top: 20rpx;
    .stance-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      margin-bottom: 16rpx;
      background-color: #fff;
      border: 2rpx solid #eaebf0;
      border-radius: 16rpx;

      .stance-content {
        flex: 1;

        .stance-label {
          display: block;
          font-size: 32rpx;
          font-weight: 550;
          color: #221d39;
        }

        .stance-company {
          display: block;
          margin-top: 8rpx;
          font-size: 28rpx;
          color: #7d7b89;
        }
      }

      &.active {
        background-color: #f2f0fc;
        border-color: #492ed1;
      }
    }
  }

  .review-header {
    display: block;
    margin-top: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #221d39;
  }

  .review-subheader {
    display: block;
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #a2a1a9;
  }

  .review-level-section {
    display: flex;
    gap: 16rpx;
    align-items: center;
    padding: 0 8rpx;
    margin-top: 32rpx;
    background: #f2f3f6;
    border-radius: 8rpx;

    .level-item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding: 16rpx;
      text-align: center;
      border-radius: 8rpx;
      .level-text {
        font-size: 28rpx;
        color: #7d7b89;
      }

      &.level-active {
        margin: 8rpx 0;
        background: #fff;
        border-radius: 8rpx;
        box-shadow: 0px 1px 2px 0px #0000000d;

        .level-text {
          font-weight: 600;
          color: #492ed1;
        }
      }
    }
  }
  .review-description {
    display: block;
    margin-top: 28rpx;
    margin-bottom: 16px;
    font-size: 24rpx;
    line-height: 1.6;
    color: #7d7b89;
    text-align: justify;
  }
}

// 按钮容器
.button-container {
  position: fixed;
  bottom: 0;
  display: flex;
  width: 100vw;
  height: 176rpx;
  background-color: #fff;
  .start-review-btn {
    flex: 1;
    height: 88rpx;
    margin: 0 32rpx;
    margin-top: 24rpx;
    font-size: 32rpx;
    color: #ffffff;
    background-color: #773bef;
    border: none;
    border-radius: 8rpx;
  }
}

.popup-content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  --wot-progress-height: 8rpx;
  .upload-status-img {
    width: 144rpx;
    height: 144rpx;
  }
  :deep(.wd-progress) {
    padding: 0;
    margin-top: 32rpx;
  }
  .status-text {
    margin-top: 32rpx;
    font-size: 32rpx;
    color: #221d39;
  }
  .confirm-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #221d39;
  }
  .popup-content-footer {
    position: fixed;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 112rpx;
    background-color: #fff;
    border-top: 2rpx solid #eaebf0;
    .action-button {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
    }
    .divider {
      width: 2rpx;
      height: 100%;
      background-color: #e9e9e9;
    }
    .cancel-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #464359;
    }
    .cancel-review-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #492ed1;
    }
    .confirm-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #492ed1;
    }
  }
}
:deep(.wd-cell__wrapper) {
  padding: 12rpx 20rpx 12rpx 0 !important;
}
:deep(.wd-cell__label) {
  margin-top: 0 !important;
}
</style>
