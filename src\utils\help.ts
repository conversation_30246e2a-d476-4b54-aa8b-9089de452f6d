export class ContractInfo {
  name: string = ''
  url: string = ''
  contractId = ''
  contractUrl: string = ''
  contractName: string = ''
  isDocFile: boolean = false
  reviewId: string = ''
  id: string = ''
  isReviewed: boolean = false
  progress: number = 0
  constructor(data?: any) {
    if (data) {
      this.name = data.contractName
      this.contractName = data.contractName
      this.contractUrl = data.originalFileCode
      this.url = data.originalFileCode
      this.isDocFile = true
      this.isReviewed = true
      this.progress = data.reviewRecordProgress
      this.contractId = data.contractId
    }
  }

  setContractId(contractId: string) {
    this.contractId = contractId
    return this
  }

  getContractId() {
    return this.contractId
  }

  setId(id: string) {
    this.id = id
    return this
  }

  setReviewRecordId(id: string) {
    this.reviewId = id
    return this
  }

  setIsReviewed(isReviewed: boolean) {
    this.isReviewed = isReviewed
    return this
  }

  setIsDoc(isDoc: boolean) {
    this.isDocFile = isDoc
    return this
  }

  setContractName(name: string) {
    this.contractName = name
    return this
  }

  setContractUrl(url: string) {
    this.url = url
    this.contractUrl = url
    return this
  }

  setFileName(name: string) {
    this.name = name
    return this
  }

  setProgress(progress: number) {
    this.progress = progress
    return this
  }
}
