import pagesConfig from '@/pages.json'
import { isMpWeixin } from './platform'

const { pages, subPackages, tabBar = { list: [] } } = { ...pagesConfig }

export const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包会报错，所以改用下面这个【虽然我加了 src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/** 判断当前页面是否是 tabbar 页  */
export const getIsTabbar = () => {
  try {
    const lastPage = getLastPage()
    const currPath = lastPage?.route

    return Boolean(tabBar?.list?.some((item) => item.pagePath === currPath))
  } catch {
    return false
  }
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 '/pages/login/index'
 * redirectPath 如 '/pages/demo/base/route-interceptor'
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 这里设计得通用一点，可以传递 key 作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的 pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)

/**
 * 根据微信小程序当前环境，判断应该获取的 baseUrl
 */
export const getEnvBaseUrl = () => {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl
        break
      case 'trial':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl
        break
      case 'release':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl
        break
    }
  }

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的 UPLOAD_BASEURL
 */
export const getEnvBaseUploadUrl = () => {
  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl
        break
      case 'trial':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl
        break
      case 'release':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl
        break
    }
  }

  return baseUploadUrl
}

export const getEnvReviewDetailUrl = () => {
  return `${getEnvBaseUrl()}/review/view`
}

// 节流函数 - 立即执行，然后在指定时间内忽略后续调用
export const throttle = <T extends (...args: any[]) => any>(func: T, delay: number = 500): T => {
  let isThrottled = false
  return ((...args: any[]) => {
    if (isThrottled) {
      return // 在节流期间，忽略后续调用
    }

    func(...args) // 立即执行
    isThrottled = true

    setTimeout(() => {
      isThrottled = false // 解除节流
    }, delay)
  }) as T
}
// 实现一个数组的深拷贝

/**
 * 深拷贝数组
 * @template T 数组元素类型
 * @param {T[]} arr 要拷贝的数组
 * @param {WeakMap<object, any>} [visited=new WeakMap()] 用于检测循环引用的 WeakMap
 * @returns {T[]} 深拷贝后的新数组
 */
export const deepCloneArray = <T>(arr: T[], visited: WeakMap<object, any> = new WeakMap()): T[] => {
  // 处理 null 或非数组的情况
  if (!Array.isArray(arr)) {
    return arr
  }

  // 检查循环引用
  if (visited.has(arr as object)) {
    return visited.get(arr as object)
  }

  // 创建新数组并存储到 WeakMap 中
  const clone: T[] = []
  visited.set(arr as object, clone)

  // 递归处理数组元素
  arr.forEach((item, index) => {
    if (item === null || item === undefined) {
      clone[index] = item
      return
    }

    if (typeof item === 'object') {
      if (item instanceof Date) {
        clone[index] = new Date(item.getTime()) as unknown as T
      } else if (item instanceof RegExp) {
        clone[index] = new RegExp(item) as unknown as T
      } else if (Array.isArray(item)) {
        clone[index] = deepCloneArray(item, visited) as unknown as T
      } else {
        // 处理普通对象
        const obj: Record<string, any> = {}
        visited.set(item as object, obj)
        Object.keys(item as object).forEach((key) => {
          obj[key] = deepCloneArray((item as Record<string, any>)[key], visited)
        })
        clone[index] = obj as unknown as T
      }
    } else {
      // 处理基本类型
      clone[index] = item
    }
  })

  return clone
}

export const autoUpdate = () => {
  // 获取小程序更新机制的兼容，由于更新的功能基础库要1.9.90以上版本才支持，所以此处要做低版本的兼容处理
  if (uni.canIUse('getUpdateManager')) {
    // wx.getUpdateManager接口，可以获知是否有新版本的小程序、新版本是否下载好以及应用新版本的能力，会返回一个UpdateManager实例
    const updateManager = uni.getUpdateManager()
    // 检查小程序是否有新版本发布，onCheckForUpdate：当小程序向后台请求完新版本信息，会通知这个版本告知检查结果
    updateManager.onCheckForUpdate((res) => {
      // 请求完新版本信息的回调
      console.log('res.hasUpdate ==> ', res.hasUpdate)
      if (res.hasUpdate) {
        // 检测到新版本，需要更新，给出提示
        uni.showModal({
          title: '更新提示',
          content: '检测到新版本，是否下载新版本并重启小程序',
          success: (res) => {
            if (res.confirm) {
              // 用户确定更新小程序，小程序下载和更新静默进行
              downLoadAndUpdate(updateManager)
            } else if (res.cancel) {
              // 若用户点击了取消按钮，二次弹窗，强制更新，如果用户选择取消后不需要进行任何操作，则以下内容可忽略
              uni.showModal({
                title: '提示',
                content: '本次版本更新涉及到新功能的添加，旧版本将无法正常使用',
                showCancel: false, // 隐藏取消按钮
                confirmText: '确认更新', // 只保留更新按钮
                success: (res) => {
                  if (res.confirm) {
                    // 下载新版本，重启应用
                    downLoadAndUpdate(updateManager)
                  }
                },
              })
            }
          },
        })
      }
    })
  } else {
    // 在最新版本客户端上体验小程序
    uni.showModal({
      title: '提示',
      content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试',
    })
  }
}
// 下载小程序最新版本并重启
const downLoadAndUpdate = (updateManager: any) => {
  uni.showLoading()
  // 静默下载更新小程序新版本，onUpdateReady：当新版本下载完成回调
  setTimeout(() => {
    uni.hideLoading()
  }, 1000 * 10)
  updateManager.onUpdateReady(() => {
    uni.hideLoading()
    // applyUpdate：强制当前小程序应用上新版本并重启
    updateManager.applyUpdate()
  })
  // onUpdateFailed：当新版本下载失败回调
  updateManager.onUpdateFailed(() => {
    // 下载新版本失败
    uni.hideLoading()
    uni.showModal({
      title: '已有新版本',
      content: '新版本已经上线了，请删除当前小程序，重新搜索打开',
    })
  })
}
