import { http } from '@/utils/http'
const API_REQUEST_PATH = '/api/bff-iterms-saas'
export const checkWxAuth = (code: string) =>
  http<API.User>({
    url: API_REQUEST_PATH + '/login/wxAuthCode',
    method: 'POST',
    data: {
      code,
    },
  })

export const wxLogin = (encryptedData: string, iv: string, token: string) =>
  http<API.User>({
    header: {
      fdd_wx_login_token: token,
    },
    url: API_REQUEST_PATH + '/login/wxLogin',
    method: 'POST',
    data: {
      encryptedData,
      iv,
    },
  })

export const getUserInfo = () =>
  http<API.User>({
    url: API_REQUEST_PATH + '/account/user/get',
    method: 'GET',
  })

export const logout = () =>
  http<API.User>({
    url: API_REQUEST_PATH + '/account/logout',
    method: 'GET',
  })

export const scanLogin = (qrCodeId: string) =>
  http<API.User>({
    url: API_REQUEST_PATH + '/account/scanLogin',
    method: 'POST',
    data: {
      qrCodeId,
    },
  })
