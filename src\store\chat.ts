import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useChatStore = defineStore(
  'chat',
  () => {
    // 聊天相关状态
    const chatId = ref('')
    const chatTitle = ref('')
    const inputValue = ref('')
    const isDeepThinking = ref(false)
    const isInternetSearchEnabled = ref(true)

    // 过程中的状态
    const isGenerating = ref(false)

    // 深度思考功能
    const toggleDeepThinking = () => {
      isDeepThinking.value = !isDeepThinking.value
    }

    // 联网搜索功能
    const toggleInternetSearch = () => {
      isInternetSearchEnabled.value = !isInternetSearchEnabled.value
    }

    // 更新输入值
    const updateInputValue = (value: string) => {
      inputValue.value = value
    }

    // 清空输入值
    const clearInputValue = () => {
      inputValue.value = ''
    }

    return {
      // 聊天状态
      chatId,
      chatTitle,
      isDeepThinking,
      isInternetSearchEnabled,
      inputValue,
      isGenerating,

      // 聊天功能方法
      toggleDeepThinking,
      toggleInternetSearch,
      updateInputValue,
      clearInputValue,
    }
  },
  {
    persist: true,
  },
)
