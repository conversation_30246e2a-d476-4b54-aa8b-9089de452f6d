<template>
  <view
    v-if="visible"
    class="scroll-to-bottom-btn"
    :style="{ bottom: computedBottom }"
    @click="handleClick"
  >
    <image
      src="@/static/images/arrow-left.svg"
      class="scroll-icon"
      mode="aspectFit"
      alt="scroll to bottom"
    />
  </view>
</template>

<script lang="ts" setup>
interface Props {
  visible: boolean
  bottomOffset?: number | string
  safeAreaOffset?: number
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  bottomOffset: 236,
  safeAreaOffset: 0,
})

const emit = defineEmits<Emits>()

// 计算底部位置
const computedBottom = computed(() => {
  const offset =
    typeof props.bottomOffset === 'number' ? `${props.bottomOffset}rpx` : props.bottomOffset
  if (props.safeAreaOffset) {
    return `calc(${offset} + ${props.safeAreaOffset}px)`
  }
  return `calc(${offset} + constant(safe-area-inset-bottom))` // iOS 11.0-11.2
})

const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.scroll-to-bottom-btn {
  position: fixed;
  left: 50%;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: #ffffff;
  border-radius: 50%;
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.15),
    0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  transform: translateX(-50%);

  &:active {
    background: #f5f5f5;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    transform: translateX(-50%) scale(0.95);
  }

  .scroll-icon {
    width: 40rpx;
    height: 40rpx;
    filter: brightness(0.4); // 将图标颜色调暗，增强对比度
    transform: rotate(-90deg); // 将左箭头旋转为向下箭头
  }
}
</style>
