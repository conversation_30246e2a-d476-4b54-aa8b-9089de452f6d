import { http } from '@/utils/http'

/** GET 请求 */
export const getRuleTypePositionList = async () => {
  return http.get('/api/bff-iterms-saas/llm/get-rule-type-position-list')
}

// 发起合同审查
export const initiateReview = (data) => {
  return http.post('/api/bff-iterms-saas/llm/contract-review', data)
}

export const queryRuleName = (data) => {
  return http.post('/api/bff-iterms-saas/llm/ai-get-rule-type-name', data)
}

export const getRecentList = async () => {
  return http.get(`/api/bff-iterms-saas/llm/llm-review-record/recent-list`)
}

export const queryReviewRuleTree = (ruleCode) => {
  return http.get(`/api/bff-iterms-saas/llm/llm-review-rule-tree?ruleCode=${ruleCode}`)
}

export const queryReviewInfo = (recordId) => {
  return http.get(`/api/bff-iterms-saas/llm/llm-review-record/review-record/${recordId}`)
}

// 分页查询审查列表
export const getReviewRecordPageList = (data) => {
  return http.post('/api/bff-iterms-saas/llm/llm-review-record/page', data)
}

// 创建新的对话
export const createNewChat = (data: { chatTitle: string }) => {
  return http.post('/api/bff-iterms-saas/chat/createChat', data)
}

// 终止对话
export const stopChat = (data: { taskId: string }) => {
  return http.post('/api/bff-iterms-saas/chat/stopChat', data)
}

export const createContract = (data: { fileName: string; fileCode: string }) => {
  return http.post('/api/bff-iterms-saas/llm/llm-contract/create', data)
}

// 分页查询合同列表
export const getContractList = (data) => {
  return http.get('/api/bff-iterms-saas/llm/llm-contract/page', data)
}

// 查询合同详情
export const queryContractInfoByContractId = (contractId: string) => {
  return http.get(
    `/api/bff-iterms-saas/llm/llm-review-record/review-record-by-contract/${contractId}`,
  )
}
