import { defineStore } from 'pinia'
import { reactive, ref, provide } from 'vue'
import { ContractInfo } from '@/utils/help'
// import ReviewApi from '@/services/effective'

export const useReviewStore = defineStore('review', () => {
  const currentLevel = ref(0)
  const contractInfo = ref<ContractInfo>(new ContractInfo())
  const reviewStatus = ref([
    { value: '-1', label: '审查失败', color: '#E6555E' },
    { value: '0', label: '排队中', color: 'rgba(73, 46, 209, 1)' },
    { value: '1', label: '审查中', color: 'rgba(73, 46, 209, 1)' },
    { value: '100', label: '审查成功', color: '#1B9275' },
  ])

  //   const saveContractInfo = (info) => {
  //     sessionStorage.setItem('contractInfo', JSON.stringify(info))
  //   }
  const removeContractInfo = () => {
    // sessionStorage.removeItem('contractInfo')
    contractInfo.value = new ContractInfo()
  }
  const getContractInfo = (): ContractInfo => {
    return contractInfo.value
  }

  const formData = ref({
    standpoint: '',
    list: '',
  })
  const defaultKeys = reactive({
    checkDefaultKeys: <string[]>[],
    customDefaultKeys: <string[]>[],
  })

  //   async function getReviewInfoById(id: string) {
  //     const { data } = await ReviewApi.queryReviewInfoByRecordId(id)

  //     contractInfo.value = new ContractInfo(data)
  //     contractInfo.value.setId(id)
  //     // saveContractInfo(contractInfo.value)
  //   }

  return {
    formData,
    defaultKeys,
    // saveContractInfo,
    getContractInfo,
    removeContractInfo,
    reviewStatus,
    currentLevel,
    contractInfo,
  }
})
