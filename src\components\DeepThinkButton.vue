<template>
  <view
    class="function-btn"
    :class="{ active: chatStore.isDeepThinking }"
    @click="toggleActive"
    :style="customStyle"
  >
    <image
      v-show="!chatStore.isDeepThinking"
      class="btn-icon"
      src="../static/images/deep-think.svg"
      mode="aspectFit"
    />
    <image
      v-show="chatStore.isDeepThinking"
      class="btn-icon"
      src="../static/images/deep-think-active.svg"
      mode="aspectFit"
    />
    <text class="btn-text">深度思考(R1)</text>
  </view>
</template>

<script setup lang="ts">
import { useChatStore } from '@/store'
const chatStore = useChatStore()

const props = defineProps({
  customStyle: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['change'])
const toggleActive = () => {
  chatStore.toggleDeepThinking()
  uni.vibrateShort()
  emit('change', chatStore.isDeepThinking)
}
</script>

<script lang="ts">
export default { name: 'DeepThinkButton' }
</script>

<style lang="scss" scoped>
.function-btn {
  gap: 8rpx;
  height: 56rpx;
  padding: 0 12rpx;
  line-height: 56rpx;
  background-color: #fff;
  border: 2rpx solid #d0d1dc;
  border-radius: 28rpx;
  transition: all 0.15s ease;
  @include flex-layout(row, center, center);

  .btn-icon {
    width: 32rpx;
    height: 32rpx;
  }

  .btn-text {
    font-size: 24rpx;
    color: --text-color;
    transition: color 0.15s ease;
  }

  &:active {
    background-color: #f0f0ff;
  }

  &.active {
    background-color: rgba(73, 46, 209, 0.07);
    border-color: #492ed1;

    .btn-text {
      color: #492ed1;
    }
  }
}
</style>
