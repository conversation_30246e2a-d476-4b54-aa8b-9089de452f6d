<template>
  <view v-if="show" class="async-picker-mask" @tap="handleMaskClick">
    <view class="async-picker-container" @tap.stop>
      <!-- 头部 -->
      <view class="async-picker-header">
        <text class="cancel-btn" @tap="handleCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm-btn" @tap="handleConfirm">确定</text>
      </view>

      <!-- 搜索框 -->
      <view v-if="showSearch" class="search-container">
        <view class="search-box">
          <input
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索..."
            @input="handleSearch"
            @confirm="handleSearchConfirm"
          />
          <text v-if="searchKeyword" class="clear-btn" @tap="clearSearch">×</text>
        </view>
      </view>

      <!-- 选项列表 -->
      <scroll-view
        scroll-y
        class="async-picker-scroll"
        :scroll-top="scrollTop"
        @scrolltolower="loadMore"
        :lower-threshold="100"
      >
        <view class="async-picker-list">
          <view
            v-for="(item, index) in displayOptions"
            :key="getItemKey(item, index)"
            class="async-picker-item"
            :class="{ 'async-picker-item--active': isSelected(item) }"
            @tap="handleSelect(item)"
          >
            <text class="item-label">{{ getItemLabel(item) }}</text>
            <image
              v-if="isSelected(item)"
              src="@/static/images/selected.svg"
              class="selected-icon"
            />
          </view>

          <!-- 加载状态 -->
          <view v-if="loading && displayOptions.length > 0" class="loading-item">
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 空状态 -->
          <view v-if="!loading && displayOptions.length === 0" class="empty-item">
            <text class="empty-text">{{ searchKeyword ? '暂无搜索结果' : '暂无数据' }}</text>
          </view>

          <!-- 没有更多 -->
          <view
            v-if="!loading && hasMore === false && displayOptions.length > 0"
            class="no-more-item"
          >
            <text class="no-more-text">没有更多了</text>
          </view>
        </view>
      </scroll-view>

      <view class="async-picker-safe-area"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'

// 选项接口
interface PickerOption {
  label: string
  value: any
  [key: string]: any
}

// API响应接口
interface ApiResponse<T> {
  data: T[]
  total: number
  hasMore?: boolean
}

// 组件属性
const props = defineProps({
  // 是否显示选择器
  show: {
    type: Boolean,
    default: false,
  },
  // 当前选中的值
  value: {
    type: [String, Number, Object, Array],
    default: '',
  },
  // 选择器标题
  title: {
    type: String,
    default: '请选择',
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 每页数据量
  pageSize: {
    type: Number,
    default: 20,
  },
  // API请求函数
  api: {
    type: Function as PropType<
      (params: { page: number; pageSize: number; keyword?: string }) => Promise<ApiResponse<any>>
    >,
    required: true,
  },
  // 选项的label字段名
  labelField: {
    type: String,
    default: 'label',
  },
  // 选项的value字段名
  valueField: {
    type: String,
    default: 'value',
  },
  // 选项的唯一标识字段名
  keyField: {
    type: String,
    default: 'value',
  },
  // 点击蒙层是否关闭
  closeOnClickMask: {
    type: Boolean,
    default: true,
  },
  // 搜索防抖时间(ms)
  searchDebounce: {
    type: Number,
    default: 500,
  },
})

// 事件定义
const emit = defineEmits(['update:show', 'update:value', 'cancel', 'confirm', 'select'])

// 响应式数据
const displayOptions = ref<PickerOption[]>([])
const selectedItems = ref<PickerOption[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const searchKeyword = ref('')
const scrollTop = ref(0)
const searchTimer = ref<any>()

// 计算属性
const isMultiple = computed(() => props.multiple)

// 获取选项的显示文本
const getItemLabel = (item: any) => {
  return item[props.labelField] || item.label || String(item)
}

// 获取选项的值
const getItemValue = (item: any) => {
  return item[props.valueField] || item.value || item
}

// 获取选项的唯一标识
const getItemKey = (item: any, index: number) => {
  return item[props.keyField] || item.value || item.id || index
}

// 判断选项是否被选中
const isSelected = (item: any) => {
  const itemValue = getItemValue(item)
  if (isMultiple.value) {
    return Array.isArray(props.value) && props.value.some((v) => v === itemValue)
  }
  return props.value === itemValue
}

// 加载数据
const loadData = async (page = 1, keyword = '', reset = false) => {
  if (loading.value) return

  loading.value = true

  try {
    const response = await props.api({
      page,
      pageSize: props.pageSize,
      keyword: keyword.trim(),
    })

    const newOptions = response.data.map((item) => ({
      ...item,
      label: getItemLabel(item),
      value: getItemValue(item),
    }))

    if (reset || page === 1) {
      displayOptions.value = newOptions
    } else {
      displayOptions.value.push(...newOptions)
    }

    hasMore.value = response.hasMore !== false && newOptions.length === props.pageSize
    currentPage.value = page
  } catch (error) {
    console.error('AsyncPicker: 加载数据失败', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadData(currentPage.value + 1, searchKeyword.value)
  }
}

// 搜索处理
const handleSearch = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(() => {
    currentPage.value = 1
    hasMore.value = true
    loadData(1, searchKeyword.value, true)
  }, props.searchDebounce)
}

// 搜索确认
const handleSearchConfirm = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
  currentPage.value = 1
  hasMore.value = true
  loadData(1, searchKeyword.value, true)
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  hasMore.value = true
  loadData(1, '', true)
}

// 选择项目
const handleSelect = (item: PickerOption) => {
  const itemValue = getItemValue(item)

  if (isMultiple.value) {
    const currentValues = Array.isArray(props.value) ? [...props.value] : []
    const index = currentValues.findIndex((v) => v === itemValue)

    if (index > -1) {
      currentValues.splice(index, 1)
      selectedItems.value = selectedItems.value.filter((i) => getItemValue(i) !== itemValue)
    } else {
      currentValues.push(itemValue)
      selectedItems.value.push(item)
    }

    emit('update:value', currentValues)
  } else {
    selectedItems.value = [item]
    emit('update:value', itemValue)
  }

  emit('select', item)
}

// 点击蒙层
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    emit('update:show', false)
    emit('cancel')
  }
}

// 取消
const handleCancel = () => {
  emit('update:show', false)
  emit('cancel')
}

// 确认
const handleConfirm = () => {
  if (isMultiple.value) {
    emit('confirm', selectedItems.value)
  } else {
    emit('confirm', selectedItems.value[0] || null)
  }
  emit('update:show', false)
}

// 初始化选中项
const initSelectedItems = () => {
  if (isMultiple.value && Array.isArray(props.value)) {
    selectedItems.value = displayOptions.value.filter(
      (item) => Array.isArray(props.value) && props.value.includes(getItemValue(item)),
    )
  } else if (!isMultiple.value && props.value) {
    const selectedItem = displayOptions.value.find((item) => getItemValue(item) === props.value)
    selectedItems.value = selectedItem ? [selectedItem] : []
  }
}

// 监听显示状态
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 重置状态
      searchKeyword.value = ''
      currentPage.value = 1
      hasMore.value = true
      displayOptions.value = []

      // 加载初始数据
      nextTick(() => {
        loadData(1, '', true)
      })
    }
  },
)

// 监听选项变化，更新选中项
watch(
  () => displayOptions.value,
  () => {
    initSelectedItems()
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.async-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.async-picker-container {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.async-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .cancel-btn,
  .confirm-btn {
    font-size: 32rpx;
    color: #666;
  }

  .confirm-btn {
    color: #007aff;
  }

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.search-container {
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .search-box {
    position: relative;
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 16rpx;

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .clear-btn {
      padding: 8rpx;
      font-size: 32rpx;
      color: #999;
    }
  }
}

.async-picker-scroll {
  max-height: 60vh;
}

.async-picker-list {
  padding: 0 32rpx;
}

.async-picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .item-label {
    flex: 1;
    font-size: 32rpx;
    color: #333;
  }

  .selected-icon {
    width: 32rpx;
    height: 32rpx;
  }

  &--active {
    .item-label {
      color: #007aff;
    }
  }
}

.loading-item,
.empty-item,
.no-more-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 0;

  .loading-text,
  .empty-text,
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}

.async-picker-safe-area {
  height: env(safe-area-inset-bottom);
}
</style>
