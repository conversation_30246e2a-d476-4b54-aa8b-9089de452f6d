// SCSS mixin 公共 flex 布局方法 - 支持参数
@mixin flex-layout($direction: row, $align: center, $justify: flex-start, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
  align-items: $align;
  justify-content: $justify;
}

@mixin outline-box($color: red) {
  outline: 1px solid $color;
}

@mixin text-ellipsis($line: 1) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

.flex-left {
  @include flex-layout(row, center);
}
.flex-left-y {
  @include flex-layout(column, center);
}

.flex-center {
  @include flex-layout(row, center, center);
}

.flex-center-y {
  @include flex-layout(column, center, center);
}

.bottom-safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 隐藏滚动条
@mixin hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
