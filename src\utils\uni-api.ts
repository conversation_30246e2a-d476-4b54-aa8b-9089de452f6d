// utils/uni-api.js
// 封装常用的uni-app API

/**
 * 显示Toast消息
 * @param {string} title 消息内容
 * @param {string} icon 图标类型 success/error/none
 * @param {number} duration 显示时长
 */
export const showToast = (title, icon = 'success', duration = 2000) => {
  uni.showToast({
    title,
    icon,
    duration,
  })
}

/**
 * 显示加载提示
 * @param {string} title 提示内容
 */
export const showLoading = (title = '加载中...') => {
  uni.showLoading({
    title,
    mask: true,
  })
}

/**
 * 隐藏加载提示
 */
export const hideLoading = () => {
  uni.hideLoading()
}

/**
 * 显示模态对话框
 * @param {string} title 标题
 * @param {string} content 内容
 * @param {boolean} showCancel 是否显示取消按钮
 */
export const showModal = (title, content, showCancel = true) => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      showCancel,
      success: (res) => {
        resolve(res)
      },
    })
  })
}

/**
 * 选择图片
 * @param {number} count 最多选择数量
 * @param {Array} sizeType 图片尺寸类型
 * @param {Array} sourceType 图片来源
 */
export const chooseImage = (
  count = 1,
  sizeType = ['original', 'compressed'],
  sourceType = ['album', 'camera'],
) => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 预览图片
 * @param {Array} urls 图片链接数组
 * @param {number} current 当前显示图片索引
 */
export const previewImage = (urls, current = 0) => {
  uni.previewImage({
    urls,
    current,
  })
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片路径
 */
export const saveImageToPhotosAlbum = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 设置导航栏标题
 * @param {string} title 标题
 */
export const setNavigationBarTitle = (title) => {
  uni.setNavigationBarTitle({
    title,
  })
}

/**
 * 页面跳转
 * @param {string} url 页面路径
 */
export const navigateTo = (url) => {
  uni.navigateTo({
    url,
  })
}

/**
 * 页面重定向
 * @param {string} url 页面路径
 */
export const redirectTo = (url) => {
  uni.redirectTo({
    url,
  })
}

/**
 * 返回上一页
 * @param {number} delta 返回层数
 */
export const navigateBack = (delta = 1) => {
  uni.navigateBack({
    delta,
  })
}

/**
 * 关闭所有页面，打开到应用内的某个页面
 * @param {string} url 页面路径
 */
export const reLaunch = (url) => {
  uni.reLaunch({
    url,
  })
}

/**
 * 跳转到 tabBar 页面
 * @param {string} url 页面路径
 */
export const switchTab = (url) => {
  uni.switchTab({
    url,
  })
}

/**
 * 获取当前页面栈
 */
export const getCurrentPages = () => {
  return getCurrentPages()
}

/**
 * 设置剪贴板内容
 * @param {string} data 内容
 */
export const setClipboardData = (data) => {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 获取剪贴板内容
 */
export const getClipboardData = () => {
  return new Promise((resolve, reject) => {
    uni.getClipboardData({
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 获取网络类型
 */
export const getNetworkType = () => {
  return new Promise((resolve, reject) => {
    uni.getNetworkType({
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 扫码
 */
export const scanCode = () => {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 获取位置信息
 * @param {string} type 坐标系类型
 */
export const getLocation = (type = 'wgs84') => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 打开位置
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @param {string} name 位置名称
 * @param {string} address 地址
 */
export const openLocation = (latitude, longitude, name = '', address = '') => {
  uni.openLocation({
    latitude,
    longitude,
    name,
    address,
  })
}

/**
 * 选择位置
 */
export const chooseLocation = () => {
  return new Promise((resolve, reject) => {
    uni.chooseLocation({
      success: resolve,
      fail: reject,
    })
  })
}
