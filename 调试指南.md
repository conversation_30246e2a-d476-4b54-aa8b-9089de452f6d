# SSE数据丢失调试指南

## 调试步骤

### 0. 验证调试工具是否可用

在微信开发者工具控制台中执行验证脚本：

```javascript
// 复制 debug-verify.js 文件内容到控制台执行
// 或者直接运行：
if (typeof globalThis.debugChunks === 'undefined') {
  console.error('❌ 调试工具未加载，请确保已进入聊天页面')
} else {
  console.log('✅ 调试工具可用')
  console.log('当前chunk数量:', globalThis.debugChunks.view().length)
}
```

### 1. 触发问题

1. 在预览模式或体验版小程序中进行聊天
2. 等待SSE响应过程中出现数据丢失或乱码

### 2. 查看实时数据

在开发者工具控制台中执行：

```javascript
// 查看当前保存的所有chunk概览
globalThis.debugChunks.view()

// 查看详细的chunk信息表格
globalThis.debugChunks.info()
```

### 3. 导出数据进行分析

```javascript
// 导出完整数据到剪贴板
globalThis.debugChunks.export()

// 或者保存到文件（小程序沙盒目录）
globalThis.debugChunks.saveToFile()
```

### 4. 分析数据问题

#### 检查点1：字符编码问题

将导出的数据粘贴到文本编辑器中，查找：

- 中文字符是否显示为乱码 `锘?` `璇?` 等
- JSON结构是否完整
- 是否有字符被截断

#### 检查点2：数据连续性

对比相邻的chunk：

```
=== sse_chunk_1_xxx ===
Content: data: {"event":"message","answer":"你好，我是AI助手，很高兴为"}

=== sse_chunk_2_xxx ===
Content: data: {"event":"message","answer":"你服务..."}
```

看是否存在数据跳跃或重复。

#### 检查点3：二进制数据完整性

检查每个chunk的size字段，确认：

- 数据块大小是否合理
- 是否有异常小的数据块（可能是截断）
- 时间戳是否连续

### 5. 测试不同解码方法

如果发现编码问题，可以修改代码测试：

```javascript
// 在onChunkReceived中临时添加多种解码测试
;(requestTask as any).onChunkReceived((res) => {
  console.log('原始ArrayBuffer size:', res.data.byteLength)

  // 测试方法1: TextDecoder
  try {
    const decoder1 = new TextDecoder('utf-8')
    const text1 = decoder1.decode(new Uint8Array(res.data))
    console.log('TextDecoder结果:', text1.substring(0, 100))
  } catch (e) {
    console.log('TextDecoder失败:', e)
  }

  // 测试方法2: 手动UTF-8解码
  const text2 = arrayBufferToString(res.data)
  console.log('手动解码结果:', text2.substring(0, 100))

  // 测试方法3: 强制ASCII解码
  const uint8Array = new Uint8Array(res.data)
  let asciiText = ''
  for (let i = 0; i < uint8Array.length; i++) {
    asciiText += String.fromCharCode(uint8Array[i])
  }
  console.log('ASCII解码结果:', asciiText.substring(0, 100))
})
```

### 6. 对比环境差异

#### 真机调试模式 vs 预览模式

1. 在真机调试模式下进行相同的对话
2. 导出数据进行对比
3. 查看是否有以下差异：
   - 数据块大小分布不同
   - 字符编码结果不同
   - 数据到达顺序不同

#### 网络环境测试

```javascript
// 添加网络信息日志
uni.getNetworkType({
  success: (res) => {
    console.log('网络类型:', res.networkType)
    console.log('是否计费网络:', res.isConnected)
  },
})
```

### 7. 问题定位

根据调试结果，常见问题类型：

#### 问题类型A：UTF-8解码错误

**症状**: 中文字符显示为乱码
**解决**: 使用TextDecoder或改进的手动解码

#### 问题类型B：数据块截断

**症状**: JSON不完整，chunk size异常小
**解决**: 优化数据块拼接逻辑

#### 问题类型C：网络传输丢包

**症状**: 数据块间有跳跃，内容不连续
**解决**: 增加重试机制，优化超时设置

#### 问题类型D：内存限制

**症状**: 大数据块处理失败
**解决**: 分块处理，避免大数据块

### 8. 清理调试数据

调试完成后清理：

```javascript
// 清理所有调试数据
globalThis.debugChunks.clear()
```

## 常用调试命令

```javascript
// 查看chunk概览
globalThis.debugChunks.view()

// 查看详细信息表格
globalThis.debugChunks.info()

// 导出数据到剪贴板
globalThis.debugChunks.export()

// 保存数据到文件
globalThis.debugChunks.saveToFile()

// 清理数据（重要：调试完成后必须执行）
globalThis.debugChunks.clear()

// 查看存储使用情况
uni.getStorageInfo({
  success: (res) => {
    console.log('存储使用:', res)
  },
})

// 手动清理特定chunk（如果需要）
uni.removeStorageSync('sse_chunk_1_1718694600000')
```

## 数据查看位置

### 方式1：剪贴板复制（推荐）

执行 `globalThis.debugChunks.export()` 后：

1. 数据会自动复制到系统剪贴板
2. 看到提示"数据已复制到剪贴板"
3. 打开任意文本编辑器（记事本、VS Code等）
4. 粘贴数据（Ctrl+V 或 Cmd+V）
5. 保存为 `.txt` 文件进行分析

### 常见问题解决

#### 问题1：`Cannot read property 'export' of undefined`

**原因**: 调试函数还未初始化
**解决方案**:

1. 确保页面已完全加载（等待几秒后再试）
2. 检查控制台是否有 "调试工具已准备就绪" 的提示
3. 如果仍然失败，刷新页面重新加载

#### 问题2：`globalThis.debugChunks` 显示 `undefined`

**原因**: Vue组件还未挂载或脚本执行出错
**解决方案**:

1. 在控制台中执行 `console.log(globalThis.debugChunks)` 检查状态
2. 如果为 `undefined`，手动进入聊天页面重新触发组件加载
3. 检查控制台是否有JavaScript错误

#### 问题3：没有chunk数据

**原因**: 还没有进行SSE聊天或数据被清理
**解决方案**:

1. 先发起一次聊天对话
2. 等待SSE数据传输完成
3. 再执行调试命令

### 方式2：文件保存（开发环境）

执行 `globalThis.debugChunks.saveToFile()` 后：

1. 数据会保存到微信小程序沙盒目录
2. 文件路径：`${wx.env.USER_DATA_PATH}/sse_debug_时间戳.txt`
3. 注意：这个文件位于小程序内部沙盒，无法直接从电脑文件系统访问
4. 建议仍使用剪贴板方式获取数据

### 方式3：控制台输出

如果剪贴板复制失败，数据会在微信开发者工具控制台输出：

1. 查看控制台中的 `console.log('完整数据:', fullText)`
2. 复制控制台中的输出内容

### 数据存储说明

- 原始chunk数据存储在微信小程序本地存储中（`uni.setStorageSync`）
- 存储键名格式：`sse_chunk_{索引}_{时间戳}`
- **现在保存所有chunk，不再限制数量**（已移除200个的限制）
- 每个chunk包含：Base64编码的原始数据、大小、时间戳、UTF-8解码后的文本
- 导出时按照chunk索引号排序，方便对比相邻chunk
- **注意**：大量chunk会占用更多本地存储空间，调试完成后记得清理

## 注意事项

1. 调试数据会占用本地存储，记得及时清理
2. 只在开发环境下暴露调试接口
3. 导出的数据可能包含敏感信息，注意保护
4. 大量调试日志可能影响性能，生产环境要移除
