<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '微信扫码登录',
  },
}
</route>
<template>
  <view class="page">
    <CustomNavbar
      title="微信扫码登录"
      :is-need-placeholder="false"
      :show-back="true"
      :transparent="isTransparent"
    />
    <view class="logo">
      <image src="@/static/nlogo.svg" style="width: 128rpx; height: 128rpx" mode="aspectFill" />
    </view>
    <view class="btns">
      <view v-if="!userStore.loginState.isBind">
        <button v-if="!userStore.isAgreeProtocol" class="btn" @click="notAllowed">一键登录</button>
        <button v-else class="btn" open-type="getPhoneNumber" @getphonenumber="loginByWxPhoneNum">
          一键登录
        </button>
      </view>
      <button v-else class="btn" @click="loginByAccount">一键登录</button>
    </view>
    <view class="agree-bar">
      <image
        v-if="userStore.isAgreeProtocol"
        src="@/static/images/checkbox-checked.svg"
        class="checkbox"
        mode="aspectFill"
        @click="handleAgreeProtocol"
      />
      <image
        v-else
        src="@/static/images/checkbox-uncheck.svg"
        class="checkbox"
        mode="aspectFill"
        @click="handleAgreeProtocol"
      />
      <text class="normal-text">我已阅读并同意</text>
      <text class="link-text" @click="gotoProtocol(1)">《用户协议》</text>
      <text class="normal-text">和</text>
      <text class="link-text" @click="gotoProtocol(0)">《隐私政策》</text>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const isTransparent = ref(true)

const qrCodeId = ref('')
onLoad((options) => {
  if (options.scene) {
    qrCodeId.value = options.scene
  }
})

const loginByWxPhoneNum = (e: any) => {
  if (!userStore.isAgreeProtocol) {
    return
  }
  userStore.bindOrLogin(e.detail.encryptedData, e.detail.iv, qrCodeId.value)
}

const notAllowed = () => {
  uni.showToast({
    title: '请先同意协议',
    icon: 'none',
  })
}
const loginByAccount = () => {
  if (!userStore.isAgreeProtocol) {
    notAllowed()
    return
  }
  userStore.login(() => {}, qrCodeId.value)
}

const handleAgreeProtocol = () => {
  userStore.handleAgreeProtocol()
}

onPageScroll((e) => {
  isTransparent.value = e.scrollTop < 100 // 滚动超过 100px 后变为不透明
})

const gotoProtocol = (type: number) => {
  uni.navigateTo({
    url: '/pages/outUrl/outUrl?type=' + type,
  })
}

onMounted(() => {
  userStore.clearData()
  userStore.login(() => {})
})
</script>
<style lang="scss" scoped>
.page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #f6f5ff -51.61%, rgba(255, 255, 255, 0) 100%);
  .bg-image {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100vh;
  }
}
.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  &-text {
    margin-top: 36rpx;
    font-size: 40rpx;
    font-weight: 500;
    color: var(--primary-color);
  }
}
.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  .btn {
    width: 90vw;
    color: #fff;
    background: linear-gradient(90deg, #ad83ff 0%, #5d34de 50%, #492ed1 100%);
  }
  .disabled {
    width: 90vw;
    color: #dedede;
    background: linear-gradient(90deg, #ad83ff 0%, #ad83ff 50%);
  }
}

.agree-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx;
  font-size: 24rpx;
  font-weight: 400;
  .checkbox {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
  }
  .normal-text {
    color: var(--light-gray-clolor);
  }
  .link-text {
    color: var(--primary-color);
  }
}
</style>
