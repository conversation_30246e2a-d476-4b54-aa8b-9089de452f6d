import MarkdownIt from 'markdown-it'
export const enum EventTypeEnum {
  CHATAGENT = 'ChatAgent',
  FOLLOWUP = 'FollowUpQuery',
  WORKFLOW = 'Workflow',
  THOUGHT = 'thought',
  BAIDU = 'BaiduSearchWithModel',
  DEEP_THOUGHT = 'chat_reasoning',
}

export interface ChatMessage {
  chatContent: any
  feedback: string
  chatRole: string
  createTime: string | number
  chatId?: string
  messageId?: string
  messageStatus?: number
  thoughtAbout?: string
  loading?: boolean
  status?: ChatStatusEnum
  isDeepThinking?: boolean
  thinkStatus?: 'done' | 'answer' | 'error'
  isThoughtExpanded?: boolean
  caseSearchList?: any[]
  lawSearchList?: any[]
  articleSearchList?: any[]
  webSearchList?: any[]
}

export const tabStyles = {
  h1: 'font-size: 32rpx; font-weight: bold; margin-bottom: 10rpx;',
  h2: 'font-size: 30rpx; font-weight: bold; margin: 16rpx 0;',
  h3: 'font-size: 30rpx; font-weight: bold; margin: 5px 0;',
  h4: 'font-size: 30rpx; font-weight: bold; margin: 5px 0;',
  hr: 'height: 1rpx; border: none;border-bottom: 1px solid #eaebf0; margin: 16rpx 0;',
  table: 'width: 99%; margin: 16rpx 8rpx; font-size: 28rpx; border-collapse: collapse;',
  th: 'padding: 20rpx; font-weight: bold; text-align: left; background-color: #f5f5f5; border: 2rpx solid #e0e0e0;',
  td: 'padding: 20rpx; border: 2rpx solid #e0e0e0;',
  tr: '&:nth-child(even) { background-color: #fafafa; }',
  'tr:hover': 'background-color: #f0f0f0;',
  'th, td': 'word-break: break-all;',
  'td img':
    'width: 100%; height: auto; display: inline-block; vertical-align: middle; margin: 10rpx 0;',
  '.quote-marks': 'display: inline-block; max-width: 100%; height: auto; vertical-align: middle;',
  p: 'font-size: 30rpx;margin: 0.2em 0; line-height: 1.6;text-align: justify;',
  ol: 'margin: 0.5em 0; padding-left: 0; list-style: none;',
  ul: 'margin: 0.5em 0; padding-left: 0; list-style: none;',
  li: 'margin: 0;',
}

export enum ChatStatusEnum { // 对话状态
  PREPARE = 'prepare',
  // LODING = 'loading',
  PROGRESS = 'progress',
  RUNNING = 'running',
  ERROR = 'error',
  STOP = 'stop',
  // SUCCESS = 'success',
  DONE = 'done',
  FILE_CONTENT_EXTRACTOR = 'file_content_extractor',
  LAW_SEARCHING = 'law_searching',
  CASE_SEARCHING = 'case_searching',
  WEB_SEARCHING = 'web_searching',
  ARTICLE_SEARCHING = 'article_searching',
  // KNOWLEDGE_SEARCHING = 'knowledge_searching',
  DEEPTHINKING = 'deepthinking',
}

export const CHAT_STATUS_MAP = {
  [ChatStatusEnum.PREPARE]: '运行中',
  [ChatStatusEnum.PROGRESS]: '运行中',
  [ChatStatusEnum.STOP]: '已深度思考',
  [ChatStatusEnum.FILE_CONTENT_EXTRACTOR]: '文件解析中',
  [ChatStatusEnum.LAW_SEARCHING]: '法律检索中',
  [ChatStatusEnum.CASE_SEARCHING]: '类案检索中',
  [ChatStatusEnum.WEB_SEARCHING]: '联网搜索中',
  [ChatStatusEnum.ARTICLE_SEARCHING]: '文章检索中',
  [ChatStatusEnum.DEEPTHINKING]: '思考中',
  [ChatStatusEnum.RUNNING]: '生成中',
  [ChatStatusEnum.DONE]: '已完成深度思考',
  [ChatStatusEnum.ERROR]: '敏感词拦截',
}

export const arrayBufferToString = (buffer: any) => {
  const uint8Array = new Uint8Array(buffer)
  let str = ''
  for (let i = 0; i < uint8Array.length; i++) {
    str += String.fromCharCode(uint8Array[i])
  }
  // 处理中文乱码
  try {
    return decodeURIComponent(escape(str))
  } catch {
    return str
  }
}

export const getDifyThinkingContent = (text: string) => {
  const regex = /<think[^>]*>([\s\S]*?)<\/think>/i
  const match = text.match(regex)
  if (match) {
    // 去掉开头所有换行符
    return match[1].replace(/^[\r\n]+/, '')
  }
}

const markDown = new MarkdownIt({
  html: false,
  breaks: true,
  linkify: true,
  typographer: false,
})

// 自定义有序列表渲染器，保留数字序号
markDown.renderer.rules.ordered_list_open = (tokens, idx, options, env, renderer) => {
  return '<ol>\n'
}

// 自定义无序列表渲染器
markDown.renderer.rules.bullet_list_open = (tokens, idx, options, env, renderer) => {
  return '<ul>\n'
}

markDown.renderer.rules.list_item_open = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx]

  // 查找父级列表token来确定列表类型（跳过嵌套的列表）
  let listStartIdx = idx - 1
  let listType = ''
  let nestedCount = 0
  while (listStartIdx >= 0) {
    if (
      tokens[listStartIdx].type === 'ordered_list_close' ||
      tokens[listStartIdx].type === 'bullet_list_close'
    ) {
      nestedCount++
    } else if (tokens[listStartIdx].type === 'ordered_list_open') {
      if (nestedCount === 0) {
        listType = 'ordered'
        break
      }
      nestedCount--
    } else if (tokens[listStartIdx].type === 'bullet_list_open') {
      if (nestedCount === 0) {
        listType = 'bullet'
        break
      }
      nestedCount--
    }
    listStartIdx--
  }

  // 如果找到了父级有序列表，生成序号
  if (listStartIdx >= 0 && listType === 'ordered') {
    const listToken = tokens[listStartIdx]
    const startAttr = listToken.attrGet('start')
    const start = startAttr ? parseInt(startAttr) : 1

    // 计算当前项的序号（只计算同级的列表项，跳过嵌套的列表项）
    let itemIndex = 0
    let nestedLevel = 0
    for (let i = listStartIdx + 1; i < idx; i++) {
      if (tokens[i].type === 'ordered_list_open' || tokens[i].type === 'bullet_list_open') {
        nestedLevel++
      } else if (
        tokens[i].type === 'ordered_list_close' ||
        tokens[i].type === 'bullet_list_close'
      ) {
        nestedLevel--
      } else if (tokens[i].type === 'list_item_open' && nestedLevel === 0) {
        itemIndex++
      }
    }
    const itemNumber = start + itemIndex

    // 将序号信息传递给后续处理
    token.attrSet('data-list-number', itemNumber.toString())
    console.log('list_item_open: 设置序号', itemNumber, 'for item at index', idx)
    return '<li>'
  }

  // 如果找到了父级无序列表，标记为无序列表项
  if (listStartIdx >= 0 && listType === 'bullet') {
    token.attrSet('data-list-bullet', 'true')
    return '<li>'
  }

  // 默认情况
  return '<li>'
}

// 新增：自定义strong标签渲染器，在列表项的第一个strong标签中嵌入序号或黑点
markDown.renderer.rules.strong_open = (tokens, idx, options, env, renderer) => {
  // 检查是否在列表项中，且是第一个strong标签
  let listItemIdx = idx - 1
  let isFirstStrong = true
  let listType = ''

  // 向前查找list_item_open token
  while (listItemIdx >= 0) {
    if (tokens[listItemIdx].type === 'list_item_open') {
      // 继续向前查找，找到最近的列表开始标记（跳过嵌套的列表）
      let listTypeIdx = listItemIdx - 1
      let nestedListCount = 0
      while (listTypeIdx >= 0) {
        if (
          tokens[listTypeIdx].type === 'ordered_list_close' ||
          tokens[listTypeIdx].type === 'bullet_list_close'
        ) {
          nestedListCount++
        } else if (tokens[listTypeIdx].type === 'ordered_list_open') {
          if (nestedListCount === 0) {
            listType = 'ordered'
            break
          }
          nestedListCount--
        } else if (tokens[listTypeIdx].type === 'bullet_list_open') {
          if (nestedListCount === 0) {
            listType = 'bullet'
            break
          }
          nestedListCount--
        }
        listTypeIdx--
      }
      break
    }
    if (tokens[listItemIdx].type === 'strong_open') {
      isFirstStrong = false
      break
    }
    listItemIdx--
  }

  // 如果在有序列表中，且是第一个strong标签，且有序号时添加序号
  if (
    listItemIdx >= 0 &&
    isFirstStrong &&
    listType === 'ordered' &&
    tokens[listItemIdx].attrGet('data-list-number')
  ) {
    const listNumber = tokens[listItemIdx].attrGet('data-list-number')
    console.log(
      'strong_open: 添加序号',
      listNumber,
      'listType:',
      listType,
      'isFirst:',
      isFirstStrong,
    )
    return `<strong>${listNumber}.&nbsp;`
  }

  // 如果在无序列表中，且是第一个strong标签时添加黑点
  if (
    listItemIdx >= 0 &&
    isFirstStrong &&
    listType === 'bullet' &&
    tokens[listItemIdx].attrGet('data-list-bullet')
  ) {
    return `<strong>&nbsp;&nbsp;&nbsp;&nbsp;<span >•</span>&nbsp;`
  }

  return '<strong>'
}

// 新增：自定义paragraph渲染器，处理没有strong标签的列表项
markDown.renderer.rules.paragraph_open = (tokens, idx, options, env, renderer) => {
  // 检查是否在列表项中，且前面没有strong标签
  let listItemIdx = idx - 1
  let hasStrongBefore = false
  let isInListItem = false
  let listType = ''

  // 向前查找，确保我们真的在列表项内
  while (listItemIdx >= 0) {
    if (tokens[listItemIdx].type === 'list_item_open') {
      isInListItem = true
      // 继续向前查找，找到最近的列表开始标记（跳过嵌套的列表）
      let listTypeIdx = listItemIdx - 1
      let nestedListCount = 0
      while (listTypeIdx >= 0) {
        if (
          tokens[listTypeIdx].type === 'ordered_list_close' ||
          tokens[listTypeIdx].type === 'bullet_list_close'
        ) {
          nestedListCount++
        } else if (tokens[listTypeIdx].type === 'ordered_list_open') {
          if (nestedListCount === 0) {
            listType = 'ordered'
            break
          }
          nestedListCount--
        } else if (tokens[listTypeIdx].type === 'bullet_list_open') {
          if (nestedListCount === 0) {
            listType = 'bullet'
            break
          }
          nestedListCount--
        }
        listTypeIdx--
      }
      break
    }
    if (tokens[listItemIdx].type === 'list_item_close') {
      // 如果遇到list_item_close，说明我们不在列表项内
      break
    }
    if (tokens[listItemIdx].type === 'strong_open') {
      hasStrongBefore = true
    }
    listItemIdx--
  }

  // 如果在有序列表项内，且没有strong标签，且有序号时添加序号
  if (
    isInListItem &&
    listType === 'ordered' &&
    listItemIdx >= 0 &&
    !hasStrongBefore &&
    tokens[listItemIdx].attrGet('data-list-number')
  ) {
    const listNumber = tokens[listItemIdx].attrGet('data-list-number')
    console.log(
      'paragraph_open: 添加序号',
      listNumber,
      'listType:',
      listType,
      'hasStrong:',
      hasStrongBefore,
    )
    return `<p><span style="font-weight:bold">${listNumber}.&nbsp;</span>`
  }

  // 如果在无序列表项内，且没有strong标签时添加黑点
  if (
    isInListItem &&
    listType === 'bullet' &&
    listItemIdx >= 0 &&
    !hasStrongBefore &&
    tokens[listItemIdx].attrGet('data-list-bullet')
  ) {
    return `<p><span style="font-weight:bold">&nbsp;<span style="font-size:32rpx;">•</span>&nbsp;</span>`
  }

  return '<p>'
}

markDown.renderer.rules.link_open = (tokens, idx, options, evn, self) => {
  return self.renderToken(tokens, idx, options)
}

export const OutputContent = ({ content }: { content: string }) => {
  if (!content) return ''

  const text = markDown.render(content)
  const thinkRegex = /&lt;think(?:\s+.*?)?&gt;[\s\S]*?&lt;\/think&gt;/g
  const outerRegex = /\^([^]*?)\^/g
  const tempText = text.replace(thinkRegex, '')
  const result = tempText.replace(outerRegex, (match, contentInsideCaret) => {
    const innerResult = contentInsideCaret.replace(/\[([^\]]+)\]/g, (_, inside) => {
      const strs = inside.split('/')
      if (strs.length === 3) {
        return `${strs[2]}<img id="${inside}" class="quote-marks" src='https://cdn.fadada.com/dist/static/c/39/20250627113934_ae7b1a94-a489-4b0b-b74a-6279a76afca4.svg' style='width:16px;height:16px;' />`
      }
      return `<img id="${inside}" class="quote-marks" src='https://cdn.fadada.com/dist/static/c/39/20250627113934_ae7b1a94-a489-4b0b-b74a-6279a76afca4.svg' style='width:16px;height:16px;' />`
    })
    return `${innerResult}`
  })

  // console.log('result ==> ', result)
  return result
}

// 解决mp-html 渲染成rich-text 样式问题
const normalizeHtml = (html: string) => {
  // 修正标点符号换行问题：将</strong>后的标点移入标签内
  let result = html.replace(/<strong>(.*?)<\/strong>([：，。])/g, '<strong>$1$2</strong>')

  // 扩展处理范围：处理所有</strong>标签后的空白字符，不仅限于标点符号结尾
  result = result.replace(/(<\/strong>)\s+/g, '$1&nbsp;')

  return result
}

// 查找最后一条assistant消息
export const findLastAssistantMessage = (
  messagesList: ChatMessage[],
): { message: ChatMessage; index: number } | null => {
  const idx = [...messagesList].reverse().findIndex((m) => m.chatRole === 'assistant')
  if (idx !== -1) {
    const realIdx = messagesList.length - 1 - idx
    return {
      message: messagesList[realIdx],
      index: realIdx,
    }
  }
  return null
}

// 更新消息内容和状态
export const updateMessageContent = (
  message: ChatMessage,
  content: string,
  messageStatus?: number,
) => {
  message.chatContent = content
  if (messageStatus !== undefined) {
    message.messageStatus = messageStatus
  }
}

// 处理SSE数据流，解决JSON对象截断问题
export const processSSEDataWithBuffer = (
  chunk: string,
  sseDataBuffer: { value: string },
  processMessageCallback: (message: string) => void,
) => {
  try {
    // 将新接收的chunk添加到缓冲区
    sseDataBuffer.value += chunk

    // 用于存储找到的完整SSE消息
    const completeMessages: string[] = []

    // 记录已处理的最后位置
    let lastProcessedPosition = 0

    while (true) {
      // 在当前位置寻找 "data:" 开始
      const dataStart = sseDataBuffer.value.indexOf('data:', lastProcessedPosition)

      if (dataStart === -1) {
        // 没有找到更多的data:标记，退出循环
        break
      }

      // 移动到JSON内容开始位置
      const jsonStart = dataStart + 5 // "data:".length

      // 从JSON开始位置寻找完整的JSON对象
      let braceCount = 0
      let jsonEnd = -1
      let inString = false
      let escaped = false

      for (let i = jsonStart; i < sseDataBuffer.value.length; i++) {
        const char = sseDataBuffer.value[i]

        if (escaped) {
          escaped = false
          continue
        }

        if (char === '\\') {
          escaped = true
          continue
        }

        if (char === '"') {
          inString = !inString
          continue
        }

        if (!inString) {
          if (char === '{') {
            braceCount++
          } else if (char === '}') {
            braceCount--
            if (braceCount === 0) {
              // 找到完整的JSON对象结尾
              jsonEnd = i
              break
            }
          }
        }
      }

      if (jsonEnd === -1) {
        // JSON不完整，保留从dataStart开始的所有内容在缓冲区
        break
      }

      // 提取完整的SSE消息
      const completeMessage = sseDataBuffer.value.substring(dataStart, jsonEnd + 1)
      const jsonPart = completeMessage.substring(5) // 移除"data:"前缀

      // 验证JSON的有效性
      try {
        JSON.parse(jsonPart)
        completeMessages.push(completeMessage)
        // 更新已处理的最后位置
        lastProcessedPosition = jsonEnd + 1
      } catch (parseError) {
        // JSON解析失败，跳过此消息
        lastProcessedPosition = dataStart + 5
        continue
      }
    }

    // 如果处理了一些消息，更新缓冲区
    if (lastProcessedPosition > 0) {
      // 移除已处理的部分，保留未处理的部分
      sseDataBuffer.value = sseDataBuffer.value.substring(lastProcessedPosition)
    }

    // 处理所有完整的消息
    completeMessages.forEach((message) => {
      processMessageCallback(message)
    })

    // 防止缓冲区无限增长
    if (sseDataBuffer.value.length > 10000) {
      sseDataBuffer.value = ''
    }
  } catch (error) {
    // 出错时重置缓冲区，避免错误累积
    sseDataBuffer.value = ''
  }
}

/**
 * 处理助手消息的通用逻辑，解析聊天内容中的各种数据
 * @param chatContent 聊天内容数组
 * @returns 处理后的结构化数据对象
 */
export const handleAssistantMessage = (chatContent: any) => {
  let caseSearchList = []
  let lawSearchList = []
  let articleSearchList = []
  let webSearchList = []
  let completeAnswer = ''
  let thinkInfo = {
    hasThink: false,
    thoughtAbout: '',
  }

  if (Array.isArray(chatContent)) {
    chatContent.forEach((data) => {
      // 处理answer
      if (data.data.nodeType === 'answer') {
        completeAnswer = data.data.outputs.answer
      }
      // 处理各种搜索结果
      if (data.data.title === 'article_search') {
        articleSearchList = data.data?.outputs?.json?.[0]?.text || []
      }
      if (data.data.title === 'law_search') {
        lawSearchList = data.data?.outputs?.json?.[0]?.text || []
      }
      if (data.data.title === 'case_search') {
        caseSearchList = data.data?.outputs?.json?.[0]?.text || []
      }
      if (data.data.title === 'web_search') {
        webSearchList = data.data?.outputs?.json?.[0]?.result || []
      }
      // 处理think标签
      const answer = data.data?.outputs?.answer
      if (typeof answer === 'string' && answer.includes('<think>') && answer.includes('</think>')) {
        thinkInfo = {
          hasThink: true,
          thoughtAbout: getDifyThinkingContent(answer) || '',
        }
      }
    })
  }

  return {
    caseSearchList,
    lawSearchList,
    articleSearchList,
    webSearchList,
    completeAnswer,
    thinkInfo,
  }
}
