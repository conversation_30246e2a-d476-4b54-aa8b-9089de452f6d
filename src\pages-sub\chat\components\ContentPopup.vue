<template>
  <wd-popup
    v-model="visible"
    :lazy-render="false"
    position="bottom"
    custom-style="border-radius:32rpx 32rpx 0 0; width: 100vw; max-height: 70vh;z-index: 1000;"
  >
    <view class="popup-content-container">
      <view class="popup-drag-bar"></view>
      <view class="popup-title">
        <view class="confirm-text">{{ title }}</view>
        <view v-if="status" class="law-status">{{ status || '现行有效' }}</view>
      </view>
      <view v-if="visible" class="popup-content">
        <text class="confirm-content">{{ content }}</text>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
interface Props {
  modelValue: boolean
  title: string
  content: string
  status?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '',
  content: '',
  status: '',
})

const emit = defineEmits<Emits>()

// 使用computed来处理v-model双向绑定
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})
</script>

<style lang="scss" scoped>
.popup-content-container {
  position: relative;
  padding: 54rpx 24rpx 40rpx 24rpx;

  .popup-drag-bar {
    position: absolute;
    top: 16rpx;
    left: 50%;
    width: 55px;
    height: 5px;
    margin: 0;
    background: #e9e9e9;
    border-radius: 3px;
    transform: translateX(-50%);
  }

  .popup-title {
    padding: 16rpx 0;

    .confirm-text {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 1.3;
      color: var(--text-color);
      @include text-ellipsis(1);
    }

    .law-status {
      margin-top: 10rpx;
      font-size: 24rpx;
      line-height: 18px;
      color: var(--sub-text-color);
    }
  }

  .popup-content {
    min-height: 12vh;
    max-height: 48vh;
    overflow-y: auto;
    line-height: 1.7;
    color: #333;
    text-align: justify;
    word-break: break-all;
    white-space: pre-line;
    @include hide-scrollbar;

    .confirm-content {
      font-size: 28rpx;
      line-height: 1.7;
      color: var(--text-color);
      text-align: justify;
      word-break: break-all;
      white-space: pre-line;
    }
  }
}
</style>
