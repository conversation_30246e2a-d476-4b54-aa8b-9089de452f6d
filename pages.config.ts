import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'iTerms',
    navigationBarBackgroundColor: '#FFFFFF',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  // 如果不需要tabBar，可以注释掉这个配置，或者直接删除
  tabBar: {
    color: '#999999',
    selectedColor: '#492ED1',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/tabbar-chat.png',
        selectedIconPath: 'static/tabbar/tabbar-chat-active.png',
        pagePath: 'pages/tabbar/chat/index',
        text: '问答',
      },
      {
        iconPath: 'static/tabbar/personal.png',
        selectedIconPath: 'static/tabbar/personalHL.png',
        pagePath: 'pages/userCenter/userCenter',
        text: '我的',
      },
    ],
  },
  subPackages: [
    {
      // 智能问答
      root: 'pages-sub/chat',
      pages: [
        {
          path: 'history-chat',
          style: {
            navigationStyle: 'custom',
            backgroundColor: '#fafbfc',
            backgroundTextStyle: 'dark',
          },
        },
        {
          path: 'chat-detail',
          style: {
            navigationStyle: 'custom',
            backgroundColor: '#fafbfc',
            backgroundTextStyle: 'dark',
          },
        },
      ],
    },
  ],
})
