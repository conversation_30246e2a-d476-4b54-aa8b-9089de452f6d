<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '协议',
  },
}
</route>
<template>
  <web-view v-if="url" :src="url"></web-view>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
const url = ref('')
const urls = [
  'https://legal.fadada.com/policy/1920377982875222016', // 隐私政策
  'https://legal.fadada.com/policy/1920378444479348736', // 服务协议
  'https://legal.fadada.com/policy/1920378826072932352', // 账号注销协议
  'https://legal.fadada.com/policy/1920379175785611264', // 第三方信息共享清单
]
onLoad((options) => {
  if (options.type) {
    url.value = urls[options.type]
  }
})
</script>
