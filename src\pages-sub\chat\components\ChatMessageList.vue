<template>
  <view
    class="chat-content"
    v-for="(item, index) in messagesList"
    :key="index"
    :data-self="item.chatRole === 'user'"
  >
    <view v-if="item.chatRole === 'user'" class="user-content-view">
      <text class="user-content-text">
        {{ getDisplayContent(item) }}
      </text>
    </view>

    <!-- 回答 -->
    <view
      v-if="
        item.chatRole === 'assistant' &&
        (JSON.parse(item.chatContent)[0].data.outputs.answer || item.isDeepThinking)
      "
      class="assistant-content-view"
    >
      <!-- 如果开启了深度思考，打开头部 -->
      <view
        class="thinking-wrapper flex-left"
        v-if="item.isDeepThinking && item.status !== ChatStatusEnum.ERROR"
        @click="handleToggleThought(index)"
      >
        <image
          src="@/static/images/thinking.svg"
          class="deep-thinking"
          mode="aspectFit"
          alt="loading"
        />
        <view class="deep-thinking-text flex-left">
          <view>{{ CHAT_STATUS_MAP[item.status] }}</view>
          <view
            v-if="item.status !== ChatStatusEnum.STOP && item.status !== ChatStatusEnum.DONE"
            class="loading-dots"
          ></view>
        </view>
        <image
          src="@/static/images/arrow-left.svg"
          class="deep-thinking arrow-icon"
          :class="{
            arrowDown: item.isThoughtExpanded,
            arrowRight: !item.isThoughtExpanded,
          }"
          mode="aspectFit"
          alt="toggle"
        />
      </view>

      <!-- 思考内容 -->
      <view
        class="thought-wrapper"
        v-if="item.isDeepThinking && item.isThoughtExpanded && item.thoughtAbout && !item.loading"
      >
        <view v-if="item.thinkStatus === 'answer'">
          <mp-html
            :selectable="true"
            :user-select="true"
            :content="item.thoughtAbout"
            :preview-img="false"
          />
        </view>
        <text v-if="item.thinkStatus === 'done'" class="thought-about-text">
          {{ item.thoughtAbout }}
        </text>
      </view>

      <!-- 回答的内容 -->
      <template
        v-if="
          item.thinkStatus === 'done' ||
          !item.isDeepThinking ||
          item.status === ChatStatusEnum.ERROR
        "
      >
        <mp-html
          :selectable="true"
          :user-select="true"
          :content="getDisplayContent(item)"
          :preview-img="false"
          :tag-style="tabStyles"
          :lazy-load="false"
          @ready="handleMpHtmlReady"
          @imgtap="(e) => handleImageTap(item, e)"
        />
      </template>
    </view>
    <!-- loading状态 -->
    <template v-if="item.chatRole === 'assistant' && item.loading">
      <image
        src="@/static/images/answerLoading.svg"
        class="loading-spinner"
        mode="aspectFit"
        alt="loading"
      />
    </template>
    <view
      v-else-if="
        item.chatRole === 'assistant' &&
        isStopped &&
        !JSON.parse(item.chatContent)[0].data.outputs.answer &&
        !item.isDeepThinking
      "
      class="assistant-content-view"
    >
      <view>（已终止生成）</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html.vue'
import {
  OutputContent,
  ChatStatusEnum,
  CHAT_STATUS_MAP,
  tabStyles,
  ChatMessage,
  getDifyThinkingContent,
} from '../utils/chatContentUtil'

interface Props {
  messagesList: ChatMessage[]
  isStopped?: boolean
}

interface Emits {
  (e: 'toggle-thought', index: number): void
  (e: 'image-tap', item: ChatMessage, event: any): void
  (e: 'mp-html-ready'): void
}

const props = withDefaults(defineProps<Props>(), {
  messagesList: () => [],
  isStopped: false,
})

const emit = defineEmits<Emits>()

// 处理消息内容显示
const getDisplayContent = (item: ChatMessage) => {
  try {
    if (item.chatRole === 'assistant') {
      let content = ''
      const chatContent = JSON.parse(item.chatContent)
      chatContent.forEach((v: any) => {
        const answer = v.data?.outputs?.answer
        if (typeof answer === 'string') {
          const thoughtAbout = getDifyThinkingContent(answer)
          if (thoughtAbout) {
            item.thoughtAbout = thoughtAbout
          }
        }
      })
      content = chatContent
        .map((v: any) => v.data?.outputs?.answer)
        .filter((answer: any) => typeof answer === 'string' && answer.trim() !== '')
        .map((answer: string) => OutputContent({ content: answer }))
        .join('\n\n')
      // 终止状态时追加终止提示
      if (item.messageStatus === 0) {
        if (!content.includes('已终止生成')) {
          content = content + '\n(已终止生成)'
        }
      }
      return content
    } else if (item.chatRole === 'user') {
      const obj = JSON.parse(item.chatContent)
      return obj.content || ''
    }
  } catch (e) {
    console.log('e ==> ', e)
    return '[内容解析失败]'
  }
}

const handleToggleThought = (index: number) => {
  emit('toggle-thought', index)
}

const handleImageTap = (item: ChatMessage, event: any) => {
  emit('image-tap', item, event)
}

const handleMpHtmlReady = () => {
  emit('mp-html-ready')
}
</script>

<style lang="scss" scoped>
.chat-content {
  padding: 20rpx 20rpx 20rpx 20rpx;

  &:first-child {
    padding-top: 36rpx;
  }

  &[data-self='true'] {
    display: flex;
    justify-content: flex-end;
  }

  .user-content-view {
    display: inline-block;
    min-width: 80rpx;
    max-width: 90%;
    padding: 24rpx 32rpx;
    margin-right: 0;
    margin-left: auto;
    font-size: 30rpx;
    color: #ffffff;
    text-align: justify;
    word-break: break-all;
    white-space: pre-wrap;
    background: #773bef;
    border-radius: 24rpx;
    border-top-right-radius: 0;

    .user-content-text {
      display: block;
      width: 100%;
      font-size: 30rpx;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .assistant-content-view {
    display: inline-flex;
    flex-direction: column;
    min-width: 80rpx;
    max-width: 90%;
    padding: 24rpx 32rpx;
    margin-right: auto;
    margin-left: 0;
    font-size: 28rpx;
    color: #221d39;
    background: #fff;
    border-radius: 24rpx;
    border-top-left-radius: 0;

    .thought-wrapper {
      padding: 10rpx 16rpx 0 16rpx;
      margin: 16rpx 0;
      color: #a2a1a9;
      text-align: justify;
      background: rgba(247, 248, 251, 1);
      border-left-color: #eaebf0;
      border-left-style: solid;
      border-left-width: 6rpx;
    }
  }
}

.thinking-wrapper {
  box-sizing: border-box;
  display: inline-flex;
  background: #fff;
  border-radius: 24rpx;
  border-top-left-radius: 0;

  .deep-thinking {
    width: 32rpx;
    height: 32rpx;
  }

  .arrow-icon {
    width: 32rpx;
    height: 32rpx;
    transition: transform 0.3s ease;
  }

  .arrowDown {
    transform: rotate(-90deg);
  }

  .arrowRight {
    transform: rotate(90deg);
  }

  .deep-thinking-text {
    margin: 0 16rpx;
    font-size: 28rpx;
    color: var(--text-color);
  }

  .loading-dots {
    width: 20rpx;
    margin-left: 8rpx;
    font-size: 28rpx;
    color: var(--text-color);

    &::after {
      content: '.';
      animation: loadingDots 1.5s infinite;
    }
  }
}

@keyframes loadingDots {
  0% {
    content: '.';
  }
  33% {
    content: '..';
  }
  66% {
    content: '...';
  }
  100% {
    content: '.';
  }
}

// loading 旋转动画
.loading-spinner {
  display: block;
  width: 48rpx;
  height: 48rpx;
  margin-top: 16rpx;
  margin-left: 16rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
