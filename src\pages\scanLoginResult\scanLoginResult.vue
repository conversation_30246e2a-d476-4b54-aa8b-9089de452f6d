<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录成功',
  },
}
</route>
<template>
  <view class="page">
    <CustomNavbar title="微信扫码登录" :show-back="true" />
    <view class="logo">
      <image src="@/static/nlogo.svg" style="width: 166rpx; height: 166rpx" mode="aspectFill" />
      <text class="logo-text">登录成功</text>
    </view>

    <view class="btns">
      <button class="btn" @click="gotoIndex">返回小程序首页</button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
const gotoIndex = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
onMounted(() => {})
</script>
<style lang="scss" scoped>
.page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #f6f5ff -51.61%, rgba(255, 255, 255, 0) 100%);
  .bg-image {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100vh;
  }
}
.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  &-text {
    margin-top: 36rpx;
    font-size: 40rpx;
    font-weight: 500;
    color: var(--primary-color);
  }
}
.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  .btn {
    width: 90vw;
    color: #492ed1;
    border: 2rpx solid #492ed1;
  }
}

.agree-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx;
  font-size: 24rpx;
  font-weight: 400;
  .checkbox {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
  }
  .normal-text {
    color: var(--light-gray-clolor);
  }
  .link-text {
    color: var(--primary-color);
  }
}
</style>
