<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '个人中心',
    enableShareAppMessage: true,
    backgroundColor: '#f9f9fb',
    backgroundTextStyle: 'dark',
  },
}
</route>
<template>
  <view class="user-info-container">
    <CustomNavbar title="个人中心" is-need-placeholder :show-back="false" :transparent="false" />
    <view class="user-info user-top">
      <view style="display: flex" @click="login">
        <view class="avatar-box">
          <view class="avatar">
            <view v-if="isLogined">
              {{ userInfo.nickName ? userInfo.nickName.substring(0, 1).toLocaleUpperCase() : '' }}
            </view>
            <view v-else>
              <image
                style="width: 40rpx; height: 40rpx; margin-top: -8rpx"
                src="@/static/tabbar/personalHL.png"
              ></image>
            </view>
          </view>
        </view>
        <view>
          <view v-if="isLogined" class="user-name">
            {{ userInfo.nickName }}
          </view>
          <view v-else class="user-name">登录</view>
          <view class="user-tips">一键开启全新智能合同审查</view>
        </view>
      </view>
    </view>
    <view class="user-info">
      <view class="info-item">
        <view>网页版</view>
        <view style="display: flex; align-items: center">
          <view>ai.iterms.com</view>
          <view style="width: 36rpx; padding-left: 12rpx">
            <view @click="copy" class="iconfont icon-copy"></view>
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="user-info">
      <view class="info-item" @click="gotoScan">
        <view>1111</view>
        <view>
          <image class="arrow-img" src="@/static/images/arrow-right.svg"></image>
        </view>
      </view>
    </view> -->
    <view class="user-info">
      <view class="info-item" @click="gotoContactUs">
        <view class="info-item-text">联系我们</view>
        <view>
          <image class="arrow-img" src="@/static/images/arrow-right.svg"></image>
        </view>
      </view>
    </view>
    <view class="sys-info">
      <view class="info-item" style="align-items: center; height: 112rpx" @click="gotoProtocol(0)">
        <view class="info-item-text">隐私政策</view>
        <view>
          <image class="arrow-img" src="@/static/images/arrow-right.svg"></image>
        </view>
      </view>
      <view class="line"></view>
      <view class="info-item" style="align-items: center; height: 112rpx" @click="gotoProtocol(1)">
        <view class="info-item-text">服务协议</view>
        <view>
          <image class="arrow-img" src="@/static/images/arrow-right.svg"></image>
        </view>
      </view>
      <view class="line"></view>
      <view class="info-item" style="align-items: center; height: 112rpx" @click="gotoProtocol(3)">
        <view class="info-item-text">第三方信息共享清单</view>
        <view>
          <image class="arrow-img" src="@/static/images/arrow-right.svg"></image>
        </view>
      </view>
    </view>
    <view
      v-if="isLogined"
      style="display: flex; align-items: center; margin-top: 32rpx"
      @click="showLogoutPopup"
    >
      <view class="iconfont icon-exit" style="margin-right: 12rpx"></view>
      <view style="font-size: 12px; color: var(--sub-text-color)">退出登录</view>
    </view>
    <wd-popup
      v-model="popupShow"
      position="center"
      closable
      custom-style="border-radius:16rpx;width:80vw;height: 640rpx;"
      @close="closePopupShow"
    >
      <view class="contact">
        <view class="contact-info">
          <view class="contact-title">联系我们</view>
          <view class="contact-desc">请用微信扫描下方二维码</view>
        </view>
        <view class="contact-img">
          <image
            src="https://cdn.fadada.com/dist/static/c/39/20250528161527_d0226c0c-c9c5-4a3e-bcd5-8e218d8a1d1c.png"
            width="180"
            height="180"
            mode="widthFix"
          ></image>
        </view>
      </view>
    </wd-popup>
    <wd-popup
      v-model="confirmLogoutShow"
      custom-style="border-radius:16rpx;width:80vw;height: 322rpx;border:0rpx"
      position="center"
      @close="closeConfirmLogout"
    >
      <view class="confirm-box">
        <view class="confirm-info">
          <view class="confirm-title">提示</view>
          <view class="confirm-desc">确定要退出登录吗？</view>
        </view>
        <view class="confirm-btn-box">
          <view
            class="flex"
            style="width: 50%; color: var(--sub-text-color)"
            @click="closeConfirmLogout"
          >
            取消
          </view>
          <view style="width: 2rpx; height: 100%; border-left: 1px solid #eaebf0"></view>
          <view class="flex" style="width: 50%; color: var(--primary-color)" @click="logout">
            确定
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { ref, computed, onMounted } from 'vue'
import { onShareAppMessage } from '@dcloudio/uni-app'
const popupShow = ref(false)
const confirmLogoutShow = ref(false)
const userStore = useUserStore()
const closeConfirmLogout = () => {
  confirmLogoutShow.value = false
  uni.showTabBar()
}
const showLogoutPopup = () => {
  confirmLogoutShow.value = true
  uni.hideTabBar()
}
const logout = () => {
  console.log('logout')
  userStore.logout()
}
const gotoScan = () => {
  uni.navigateTo({
    url: '/pages/scanLogin/scanLogin',
  })
}
const login = () => {
  if (!userStore.isLogined) {
    uni.navigateTo({
      url: '/pages/login/login',
    })
  }
}

const gotoContactUs = () => {
  popupShow.value = true
  uni.hideTabBar()
}

const closePopupShow = () => {
  popupShow.value = false
  uni.showTabBar()
}

const gotoProtocol = (type: number) => {
  uni.navigateTo({
    url: '/pages/outUrl/outUrl?type=' + type,
  })
}
const copy = () => {
  uni
    .setClipboardData({
      data: 'https://ai.iterms.com',
    })
    .then(() => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    })
}

const isLogined = computed(() => {
  return userStore.isLogined
})

const userInfo = computed(() => {
  return userStore.userInfo
})
onShow(() => {
  userStore.loadUserInfo()
})
onBeforeUnmount(() => {
  uni.showTabBar()
})

// 分享给好友
onShareAppMessage(() => {
  return {
    title: 'iTerms 法律人的合同审查助手',
    path: '/pages/index/index',
    desc: '一键开启全新智能合同审查体验',
    imageUrl: '/static/images/share.png',
  }
})
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-info-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx 24rpx;
  align-items: center;
  justify-content: start;
  height: 100vh;
  background: #f9f9fb;
}
.arrow-img {
  width: 24rpx;
  height: 48rpx;
}
.user-info {
  display: flex;
  align-items: center;
  justify-content: start;
  width: calc(100vw - 64rpx);
  min-height: 112rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0px 1px 8rpx 0px #00000014;
}
.sys-info {
  width: calc(100vw - 64rpx);
  min-height: 224rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0px 1px 8rpx 0px #00000014;
}
.user-top {
  height: 176rpx;
  margin-top: 20rpx;
}
.avatar-box {
  padding-right: 32rpx;
  padding-left: 24rpx;
}
.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 112rpx; /* 宽度和高度必须相等 */
  height: 112rpx; /* 宽度和高度必须相等 */
  font-size: 40rpx;
  font-weight: 500;
  color: var(--primary-color);
  background-color: #492ed112; /* 背景色（带透明度的紫色） */
  border-radius: 50%; /* 将圆角半径设为宽度的一半，实现圆形 */
}
.user-name {
  height: 52rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: var(--text-color);
}
.user-tips {
  padding-top: 24rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: var(--sub-text-color);
}
.info-item {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 48rpx);
  padding: 0 24rpx;
}
.info-item-text {
  font-size: 32rpx;
  font-weight: normal;
  color: var(--text-color);
}
.line {
  width: calc(100% - 48rpx);
  margin-left: 24rpx;
  border-top: 1px solid rgb(240, 241, 245);
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}
.contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.contact-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 48rpx;
}
.contact-title {
  font-size: 36rpx;
  font-weight: 500;
  line-height: 56rpx;
  color: var(--text-color);
}
.contact-desc {
  padding-top: 32rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 40rpx;
  color: var(--sub-text-color);
}
.contact-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360rpx;
  height: 360rpx;
  margin-top: 32rpx;
}
.confirm-info {
  display: flex;
  flex: 1 auto;
  flex-direction: column;
  align-items: center;
  height: 220rpx;
}
.confirm-title {
  padding-top: 48rpx;
  font-size: 36rpx;
  font-weight: 500;
  line-height: 56rpx;
  color: var(--text-color);
}
.confirm-desc {
  padding-top: 32rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 40rpx;
  color: var(--sub-text-color);
}
.confirm-btn-box {
  display: flex;
  flex: auto;
  width: 100%;
  height: 100rpx;
  border-top: 1px solid #eaebf0;
}
.confirm-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
</style>
