<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="review-record-page">
    <CustomNavbar
      title="合同列表"
      :is-need-placeholder="false"
      :show-back="true"
      :transparent="false"
    />
    <view
      class="scroll-container"
      :style="{
        paddingTop: safeAreaInsetsTop + 'px',
        height: `calc(100vh - ${safeAreaInsetsTop}px)`,
      }"
    >
      <scroll-view
        v-if="contractsList.length > 0"
        class="scroll-view"
        scroll-y
        :lower-threshold="100"
        @scrolltolower="handleScrollToLower"
        enhanced
        :show-scrollbar="false"
      >
        <view class="review-record-container">
          <view class="review-record-content">
            <view class="review-record-list">
              <view
                v-for="(item, index) in contractsList"
                :key="index"
                class="review-record-item"
                @click="goToDetail(item)"
              >
                <view class="item-left">
                  <image src="@/static/images/record-item-img.svg" class="item-img"></image>
                  <view class="item-info">
                    <text class="item-title">{{ item.contractName }}</text>
                    <text class="item-status">
                      {{ item.contractStatus }}
                    </text>
                  </view>
                </view>
                <view class="item-right">
                  <image src="@/static/images/arrow-right.svg" class="arrow-right"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 加载状态 -->
        <view class="loading-container">
          <view v-if="noMore && contractsList.length > 0" class="no-more">
            <text class="no-more-text">没有更多数据了</text>
          </view>
          <view v-else-if="contractsList.length > 0 && !loading && isError" class="error-container">
            <text class="empty-text">出错了，请重试</text>
            <img
              src="@/static/images/refresh.svg"
              alt=""
              class="refresh-icon"
              @click="loadData()"
            />
          </view>
        </view>
      </scroll-view>
      <view v-else-if="contractsList.length === 0 && !loading && !isError" class="empty-container">
        <text class="empty-text">暂无数据</text>
      </view>
      <view v-else-if="contractsList.length === 0 && !loading && isError" class="error-container">
        <text class="empty-text">出错了，请重试</text>
        <img
          src="@/static/images/refresh.svg"
          alt=""
          class="refresh-icon"
          @click="loadData(true)"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavbar from '@/components/CustomNavbar.vue'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { getContractList } from '@/service/index/effective'
import { useReviewStore } from '@/store'

defineOptions({
  name: 'reviewRecord',
})

const reviewStore = useReviewStore()

const loading = ref(false)
const isError = ref(false)
const noMore = ref(false)
const isFirstLoad = ref(true)

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

// 防抖定时器
let scrollTimer = null

const contractsList = ref([])

// 获取屏幕边界到安全区域距离
const navBarHeight = ref(0)
const statusBarHeight = ref(0)
const safeAreaInsetsTop = ref(0)

const goToDetail = (item) => {
  if (item.contractStatus === '编辑中') {
    const isDocFile = item.contractName.endsWith('.doc') || item.contractName.endsWith('.docx')
    reviewStore.contractInfo
      .setContractName(item.contractName)
      .setIsDoc(isDocFile)
      .setFileName(item.contractName.split('.')[0])
      .setContractId(item.contractId)
    uni.navigateTo({
      url: `/pages/index/contractReview/contractReview?contractId=${item.contractId}`,
    })
  } else {
    uni.navigateTo({
      url: `/pages/index/contractReview/reviewResult?contractId=${item.contractId}`,
    })
  }
}

// 滚动到底部触发
const handleScrollToLower = () => {
  // 防抖处理
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  scrollTimer = setTimeout(() => {
    if (!loading.value && !noMore.value) {
      loadData()
    }
  }, 300)
}

const loadData = async (isRefresh = false) => {
  isError.value = false
  if (loading.value) return

  const currentPage = isRefresh ? 1 : pagination.pageNum
  try {
    loading.value = true
    const params = {
      pageNum: currentPage,
      pageSize: pagination.pageSize,
      data: {},
    }
    const { data } = await getContractList(params)
    const responseData = data as { list: any[]; total: number }

    if (isRefresh) {
      // 刷新数据
      contractsList.value = responseData.list
      pagination.pageNum = 1
      pagination.total = responseData.total
      noMore.value = false
    } else {
      // 追加数据
      contractsList.value.push(...responseData.list)
      pagination.total = responseData.total
    }

    // 检查是否还有更多数据
    const totalPages = Math.ceil(pagination.total / pagination.pageSize)
    if (currentPage >= totalPages) {
      noMore.value = true
    } else {
      pagination.pageNum = currentPage + 1
    }
  } catch (error) {
    isError.value = true
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
    isFirstLoad.value = false
  }
}

onLoad(() => {
  loadData(true)
})

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  navBarHeight.value = 44
  safeAreaInsetsTop.value = statusBarHeight.value + navBarHeight.value
})

onBeforeUnmount(() => {
  clearTimeout(scrollTimer)
})
</script>

<style lang="scss" scoped>
.review-record-page {
  height: 100vh;
  background-color: #f9f9fb;
  .scroll-container {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .scroll-view {
      height: 100%;
    }
  }

  .review-record-container {
    display: flex;
    flex-direction: column;
    .review-record-content {
      padding: 24rpx;
      margin: 32rpx;
      margin-bottom: 0;
      background-color: #ffffff;
      border-radius: 16rpx;
      box-shadow: 0px 1px 4px 0px #00000014;
      .review-record-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 2rpx solid #eaebf0;
        .item-left {
          display: flex;
          .item-img {
            width: 96rpx;
            height: 96rpx;
            margin-right: 24rpx;
          }
          .item-info {
            display: flex;
            flex-direction: column;
            .item-title {
              max-width: 500rpx;
              overflow: hidden; /* 隐藏溢出内容 */
              font-size: 32rpx;
              line-height: 48rpx;
              color: #221d39;
              text-overflow: ellipsis; /* 显示省略号 */
              white-space: nowrap; /* 强制单行显示 */
            }
            .item-status {
              margin-top: 8rpx;
              font-size: 24rpx;
              line-height: 36rpx;
              color: #a2a1a9;
            }
          }
        }
        .item-right {
          width: 24rpx;
          height: 48rpx;
          margin-right: 0;
          margin-left: auto;
          .arrow-right {
            width: 24rpx;
            height: 48rpx;
          }
        }
      }
      .review-record-item:first-of-type {
        padding-top: 0;
      }
      .review-record-item:last-of-type {
        padding-bottom: 0;
        border-bottom: none;
      }
    }
  }
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    .loading-item {
      width: 100%;
    }

    .no-more {
      text-align: center;
    }

    .no-more-text {
      font-size: 26rpx;
      color: #999;
    }
    .empty {
      text-align: center;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .empty-container,
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
    .refresh-icon {
      width: 40rpx;
      height: 40rpx;
      margin-left: 16rpx;
    }
  }
}
</style>
