// 自定义Picker组件 // components/custom-picker/index.vue

<template>
  <!-- <view class="custom-picker">
    <view
      class="custom-picker__mask"
      :class="{ 'custom-picker__mask--show': show }"
      @tap="handleMaskClick"
      @touchmove.stop.prevent
    ></view>

    <view class="custom-picker__container" :class="{ 'custom-picker__container--show': show }">
      <view class="custom-picker__header">
        <view class="custom-picker__btn custom-picker__cancel-btn" @tap="handleCancel">取消</view>
        <view class="custom-picker__title">{{ title }}</view>
        <view class="custom-picker__btn custom-picker__confirm-btn" @tap="handleConfirm">确定</view>
      </view>

      <view class="custom-picker__content">
        <scroll-view
          scroll-y
          class="custom-picker__scroll"
          :scroll-top="scrollTop"
          :style="{ maxHeight: maxHeight + 'rpx' }"
        >
          <view class="custom-picker__list">
            <view
              v-for="(item, index) in options"
              :key="index"
              class="custom-picker__item"
              :class="{ 'custom-picker__item--active': currentIndex === index }"
              @tap="handleSelect(index)"
            >
              <text class="item-label">{{ item.label }}</text>
              <image
                v-if="currentIndex === index"
                src="@/static/images/selected.svg"
                class="selected-icon"
              ></image>
            </view>
          </view>
        </scroll-view>
      </view>

      <view class="custom-picker__safe-area"></view>
    </view>
  </view> -->
  <view class="custom-picker__container">
    <view class="custom-picker__header">
      <view class="custom-picker__btn custom-picker__cancel-btn" @tap="handleCancel">取消</view>
      <view class="custom-picker__title">{{ title }}</view>
      <view class="custom-picker__btn custom-picker__confirm-btn" @tap="handleConfirm">确定</view>
    </view>

    <view class="custom-picker__content">
      <scroll-view
        scroll-y
        class="custom-picker__scroll"
        :scroll-top="scrollTop"
        :style="{ maxHeight: maxHeight + 'rpx' }"
      >
        <view class="custom-picker__list">
          <view
            v-for="(item, index) in options"
            :key="index"
            class="custom-picker__item"
            :class="{ 'custom-picker__item--active': currentIndex === index }"
            @tap="handleSelect(index)"
          >
            <text class="item-label">{{ item.label }}</text>
            <image
              v-if="currentIndex === index"
              src="@/static/images/selected.svg"
              class="selected-icon"
            ></image>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="custom-picker__safe-area"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'

// 定义选项接口
interface PickerOption {
  label: string
  value: any
}

// 定义组件的属性
const props = defineProps({
  // 是否显示选择器
  show: {
    type: Boolean,
    default: false,
  },
  // 选项数据，格式为 { label: string, value: any }[]
  options: {
    type: Array as () => PickerOption[],
    default: () => [],
  },
  // 默认选中项的value
  value: {
    type: [String, Number, Object],
    default: '',
  },
  // 选择器标题
  title: {
    type: String,
    default: '',
  },
  // 选择器内容区最大高度
  maxHeight: {
    type: Number,
    default: 500,
  },
  // 点击蒙层是否关闭选择器
  closeOnClickMask: {
    type: Boolean,
    default: true,
  },
})

// 定义组件的事件
const emit = defineEmits(['update:show', 'cancel', 'confirm', 'select'])

// 当前选中项的索引
const currentIndex = ref(-1)
const scrollTop = ref(0)

// 查找默认选中项的索引
const findDefaultIndex = () => {
  if (!props.options || props.options.length === 0) return 0

  const index = props.options.findIndex((item) => item.value === props.value)
  return index >= 0 ? index : -1
}

// 更新滚动位置，使选中项居中显示
const updateScrollTop = () => {
  // 每个选项的高度为100rpx，将选中项滚动到中间位置
  scrollTop.value = currentIndex.value * 100
}

// 监听props.value的变化，更新选中项
watch(
  () => props.value,
  () => {
    currentIndex.value = findDefaultIndex()
    updateScrollTop()
  },
  { immediate: true },
)

// 监听props.options的变化，重新计算选中项
watch(
  () => props.options,
  () => {
    currentIndex.value = findDefaultIndex()
    updateScrollTop()
  },
  { deep: true },
)

// 选中某一项
const handleSelect = (index: number) => {
  currentIndex.value = index
  emit('select', props.options[index])
}

// 点击蒙层
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    emit('update:show', false)
    emit('cancel')
  }
}

// 点击取消按钮
const handleCancel = () => {
  emit('update:show', false)
  emit('cancel')
}

// 点击确定按钮
const handleConfirm = () => {
  const selectedItem = props.options[currentIndex.value]
  emit('confirm', selectedItem)
  emit('update:show', false)
}

// 初始化
onMounted(() => {
  currentIndex.value = findDefaultIndex()
  updateScrollTop()
})
</script>

<style lang="scss" scoped>
.custom-picker {
  // 蒙层样式
  &__mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9998;
    pointer-events: none;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;

    &--show {
      pointer-events: auto;
      opacity: 1;
    }
  }

  // 容器样式
  &__container {
    // position: fixed;
    // left: 0;
    // right: 0;
    // bottom: 0;
    // z-index: 9999;
    // background-color: #fff;
    // border-radius: 24rpx 24rpx 0 0;
    // transform: translateY(100%);
    // transition: transform 0.3s ease;

    // &--show {
    //   transform: translateY(0);
    // }
  }

  // 头部样式
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }

  &__title {
    font-size: 36rpx;
    font-weight: 600;
    color: #262626;
  }

  &__btn {
    padding: 10rpx;
    font-size: 28rpx;
  }

  &__cancel-btn {
    color: #a2a1a9;
  }

  &__confirm-btn {
    color: #492ed1;
  }

  // 内容区域样式
  &__content {
    // padding: 20rpx 0;
  }

  &__scroll {
    width: 100%;
  }

  &__list {
    // padding: 0 30rpx;
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 48rpx;
    color: #333;
    border-bottom: 1rpx solid #f5f5f5;
    .item-label {
      font-size: 36rpx;
      color: #221d39;
    }

    &--active {
      .item-label {
        font-size: 36rpx;
        color: #492ed1;
      }
    }

    .selected-icon {
      width: 36rpx;
      height: 36rpx;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  // 底部安全区域
  &__safe-area {
    position: relative;
    width: 100%;
    height: 40rpx;

    &::after {
      position: absolute;
      bottom: 8rpx;
      left: 50%;
      width: 40%;
      height: 5rpx;
      content: '';
      background: #ccc;
      border-radius: 5rpx;
      transform: translateX(-50%);
    }
  }
}
</style>
