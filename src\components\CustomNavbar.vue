<template>
  <view
    class="custom-navbar"
    :class="{
      fixed: fixed,
      transparent: transparent,
      dark: theme === 'dark',
      gradient: theme === 'gradient',
    }"
    :style="{
      height: totalHeight + 'px',
      backgroundColor: computedBackgroundColor,
    }"
  >
    <!-- 状态栏占位 -->
    <view
      class="status-bar"
      :style="{
        height: statusBarHeight + 'px',
        backgroundColor: computedBackgroundColor,
      }"
    ></view>

    <!-- 导航栏主体 -->
    <view
      class="navbar-content"
      :style="{
        height: navBarHeight + 'px',
        backgroundColor: computedBackgroundColor,
      }"
    >
      <!-- 左侧内容 -->
      <view class="navbar-left">
        <view v-if="showBack" class="back-button" @click="handleBack">
          <image class="back-icon" src="@/static/images/arrow-left.svg" mode="aspectFit" />
        </view>
        <slot name="left"></slot>
      </view>

      <!-- 中间标题 -->
      <view class="navbar-center">
        <text v-if="title" class="navbar-title" :style="{ color: computedTitleColor }">
          {{ title }}
        </text>
        <slot v-else name="center">
          <image class="top-title" src="@/static/images/top-title.svg" mode="aspectFit" />
        </slot>
      </view>

      <!-- 右侧内容 -->
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
  <!-- 占位符 -->
  <view
    v-if="isNeedPlaceholder"
    :style="{
      height: totalHeight + 'px',
      width: '100vw',
    }"
  ></view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'

// 获取组件实例
// 定义 props
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '',
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: true,
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: '#ffffff',
  },
  // 标题颜色
  titleColor: {
    type: String,
    default: '#333333',
  },
  // 是否固定定位
  fixed: {
    type: Boolean,
    default: true,
  },
  // 是否透明背景
  transparent: {
    type: Boolean,
    default: false,
  },
  // 主题类型
  theme: {
    type: String,
    default: 'default', // default, dark, gradient
    validator: (value: string) => ['default', 'dark', 'gradient'].includes(value),
  },
  // 自定义是否使用默认返回行为
  useDefaultBack: {
    type: Boolean,
    default: true,
  },

  // 是否要占位元素
  isNeedPlaceholder: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits(['back'])

// 系统信息
const statusBarHeight = ref(0)
const navBarHeight = ref(44)
const totalHeight = computed(() => statusBarHeight.value + navBarHeight.value)

// 计算背景色
const computedBackgroundColor = computed(() => {
  if (props.transparent) {
    return 'transparent'
  }

  switch (props.theme) {
    case 'dark':
      return '#1a1a1a'
    case 'gradient':
      return 'transparent' // 渐变通过 CSS 处理
    default:
      return props.backgroundColor
  }
})

// 计算标题颜色
const computedTitleColor = computed(() => {
  if (props.theme === 'dark' || props.theme === 'gradient') {
    return '#ffffff'
  }
  return props.titleColor
})

// 获取系统信息
onMounted(() => {
  const windowInfo = uni.getWindowInfo()
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = windowInfo.statusBarHeight || systemInfo.statusBarHeight || 0
})

// 返回事件处理
const handleBack = () => {
  if (!props.useDefaultBack) {
    // 如果不使用默认返回行为，则直接触发自定义事件
    emit('back')
    return
  }
  setTimeout(() => {
    uni.navigateBack({
      delta: 1,
    })
  }, 0)
}
// 暴露给父组件
defineExpose({
  totalHeight,
  statusBarHeight,
  navBarHeight,
})
</script>
<script lang="ts">
export default { name: 'CustomNavbar' }
</script>
<style lang="scss" scoped>
.status-bar {
  width: 100%;
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
}

.custom-navbar {
  position: relative;
  z-index: 8;
  width: 100%;
  // 固定定位
  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
  }

  // 透明背景
  &.transparent {
    background: transparent !important;
    transition: all 0.8s ease;

    .status-bar,
    .navbar-content {
      background: transparent !important;
    }
  }

  // 渐变主题
  &.gradient {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;

    .status-bar,
    .navbar-content {
      background: inherit !important;
    }
  }

  // 毛玻璃效果（可选）
  &.blur {
    background: rgba(255, 255, 255, 0.8) !important;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    .status-bar,
    .navbar-content {
      background: inherit !important;
    }
  }
}

.navbar-left {
  display: flex;
  flex-shrink: 0;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 30rpx;
  margin-right: 8px;
  &:active {
    opacity: 0.6;
  }
}

.back-icon {
  width: 24rpx;
  height: 48rpx;
}

.navbar-center {
  position: absolute;
  left: 50%;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  max-width: 60%;
  transform: translateX(-50%);
}

.navbar-title {
  overflow: hidden;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.top-title {
  width: 120rpx;
  height: 44rpx;
}
.navbar-right {
  display: flex;
  flex-shrink: 0;
  align-items: center;
}

// 响应式适配
// #ifdef H5
@media screen and (max-width: 768px) {
  .navbar-content {
    padding: 0 12px;
  }

  .navbar-title {
    font-size: 16px;
  }
}
// #endif
</style>
