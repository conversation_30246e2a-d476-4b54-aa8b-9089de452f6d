import { http } from '@/utils/http'

// 新建一个对话 会返回一个id
export const createChat = (chatTitle: string) => {
  return http.post('/api/bff-iterms-saas/chat/createChat', { chatTitle })
}
// 第二步 拿着这个id 去查详情
export const getChatDetail = (chatId: string) => {
  return http.post('/api/bff-iterms-saas/chat/detail', { chatId })
}
// 第三步 查看对话列表
export const getChatMessages = (chatId: string) => {
  return http.post('/api/bff-iterms-saas/chat/getChatMessages', { chatId })
}

// 历史对话
export const getHistoryChat = (data: { currentPageNo: number; pageSize: number }) => {
  return http.post('/api/bff-iterms-saas/chat/page', data)
}
