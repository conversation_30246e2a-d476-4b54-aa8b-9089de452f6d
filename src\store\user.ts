import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as UserApi from '@/service/index/user'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const defaultUserInfo = { nickname: '', avatarUrl: '', userId: '' }
const defaultLoginState = { userId: '', token: '', isBind: false }
export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...defaultUserInfo })
    const loginState = ref<ILoginState>({ ...defaultLoginState })

    const setUserInfo = (info: IUserInfo) => {
      userInfo.value = info
    }

    const setLoginState = (info: ILoginState) => {
      loginState.value = info
    }
    const getToken = () => {
      return uni.getStorageSync('wx-token')
    }
    const isLogined = computed(() => {
      return !!userInfo.value.userId
    })

    const clearData = () => {
      loginState.value = { ...defaultLoginState }
      userInfo.value = { ...defaultUserInfo }
      uni.removeStorageSync('wx-token')
      isAgreeProtocol.value = false
    }

    const loadUserInfo = async () => {
      try {
        // wx-token 不存在就不请求
        if (!getToken()) return
        // console.log('getToken() ==> 123', getToken())
        const { data, code, message } = await UserApi.getUserInfo()
        if (code === RESPONSE_CODE_SUCCESS) {
          setUserInfo({ ...data })
        } else {
          uni.showToast({
            title: message,
            icon: 'none',
          })
        }
      } catch (error) {
        uni.showToast({
          title: error,
          icon: 'none',
        })
      }
    }

    const logout = async () => {
      uni.showToast({
        title: '退出成功',
        icon: 'none',
      })
      uni.reLaunch({
        url: '/pages/index/index',
      })
      try {
        await UserApi.logout()
      } finally {
        clearData()
      }
    }
    const wechatLogin = async () => {
      return new Promise((resolve, reject) => {
        uni.login({
          success: async (res) => {
            resolve(res.code)
          },
          fail: reject,
        })
      })
    }
    const checkSession = async (): Promise<boolean> => {
      try {
        await new Promise((resolve, reject) => {
          uni.checkSession({
            success: () => {
              console.log('session 有效')
              resolve(true)
            },
            fail: (error) => {
              console.log('session 已过期，需要重新登录', error)
              reject(new Error('SESSION_EXPIRED'))
            },
          })
        })
        return true
      } catch (error) {
        if (error?.message === 'SESSION_EXPIRED') {
          // session 过期是正常现象，不需要当作错误
          return false
        }
        // 其他错误才记录
        console.error('session 检查出现异常:', error)
        return false
      }
    }

    const login = async (callback?: () => void, qrCodeId?: string) => {
      console.log('login', callback, qrCodeId)
      try {
        const chatCode = (await wechatLogin()) as string
        const { code, data } = await UserApi.checkWxAuth(chatCode)
        if (code === RESPONSE_CODE_SUCCESS) {
          setLoginState({ ...data, isBind: !!data.userId })
          console.log(data.userId, data.token, isAgreeProtocol.value)
          if (data.userId && data.token && isAgreeProtocol.value) {
            uni.setStorageSync('wx-token', data.token)
            if (!callback) {
              uni.navigateBack()
            } else {
              callback()
            }
          }
          // loadUserInfo()

          if (qrCodeId) {
            checkScanResult(qrCodeId)
          }
        }
      } catch (e) {
        console.log('登录失败1', e)
        showLoginFail('login')
      }
    }
    const showLoginFail = (step) => {
      uni.showToast({
        icon: 'none',
        title: '登录失败，请稍后重试',
      })
    }
    const bindOrLogin = async (encryptedData: string, iv: string, qrCodeId?: string) => {
      console.log('bindOrLogin', encryptedData, iv, qrCodeId)
      try {
        // 1. 检查 session
        const isSessionValid = await checkSession()
        // 2. session 无效或没有 token 都需要重新登录
        if (isSessionValid) {
          const wechatCode = (await wechatLogin()) as string
          const { code, data, message } = await UserApi.checkWxAuth(wechatCode)
          if (code !== RESPONSE_CODE_SUCCESS) {
            showLoginFail('auth' + message)
            return
          }

          if (!data.userId) {
            // 需要绑定
            const result = await UserApi.wxLogin(encryptedData, iv, data.token)
            if (result.code === RESPONSE_CODE_SUCCESS) {
              uni.setStorageSync('wx-token', result.data)
            } else {
              showLoginFail('wxLogin' + result.message)
            }
          } else {
            uni.setStorageSync('wx-token', data.token)
          }
          await loadUserInfo() // 加载用户信息
          // 处理扫码登录
          if (qrCodeId) {
            checkScanResult(qrCodeId)
          } else {
            uni.navigateBack()
          }
        } else {
          showLoginFail('checkSession')
        }
      } catch (e) {
        console.error('登录过程发生错误:', e)
        const errorMsg = e?.errMsg || e?.message || '登录失败，请稍后重试'
        showLoginFail('登录过程发生错误')
      }
    }

    const checkScanResult = async (qrCodeId) => {
      console.log('checkScanResult', qrCodeId)
      try {
        const { code, message } = await UserApi.scanLogin(qrCodeId)
        uni.showToast({
          title: message,
          icon: 'none',
        })
        if (code === RESPONSE_CODE_SUCCESS) {
          uni.navigateTo({
            url: '/pages/scanLoginResult/scanLoginResult',
          })
        } else {
          showLoginFail('scanLogin')
        }
      } catch (e) {
        console.log('登录失败3', e)
        showLoginFail('scanLogin err')
      }
    }

    const getLoginStatus = () => {
      const token = getToken()
      return {
        hasToken: !!token,
        isLoggedIn: isLogined.value,
        token,
      }
    }
    const isAgreeProtocol = ref(false)
    const handleAgreeProtocol = () => {
      isAgreeProtocol.value = !isAgreeProtocol.value
      console.log(isAgreeProtocol.value)
    }
    return {
      isAgreeProtocol,
      handleAgreeProtocol,
      checkScanResult,
      getToken,
      clearData,
      isLogined,
      logout,
      login,
      loadUserInfo,
      bindOrLogin,
      loginState,
      userInfo,
      getLoginStatus,
    }
  },
  {
    persist: true,
  },
)
