<route lang="json5" type="page">
{
  style: { navigationStyle: 'custom' },
}
</route>

<template>
  <view class="chat-page-container">
    <CustomNavbar
      title="法律问答"
      :is-need-placeholder="false"
      :show-back="true"
      :transparent="false"
      ref="navbarRef"
    />
    <scroll-view
      :style="{ height: windowHeight - keyboardHeight + 'px' }"
      scroll-y
      scroll-anchoring
      :scroll-into-view="bottomView"
      :scroll-with-animation="true"
      class="chat-panel-scroll"
      :show-scrollbar="false"
      :virtual-list="true"
      :item-size="100"
      @touchstart="handleChatTouchStart"
      @scroll="handleScroll"
    >
      <view
        class="chat-content-wrapper"
        :style="{ paddingTop: navbarHeight + 'px' }"
        :class="keyboardHeight ? 'bottom-0' : ''"
      >
        <ChatMessageList
          :messages-list="chatMessagesList"
          :is-stopped="isStopped"
          @toggle-thought="toggleThoughtExpanded"
          @image-tap="onImgTap"
          @mp-html-ready="
            () => {
              nextTick(() => {
                scrollToBottom(100)
              })
            }
          "
        />
      </view>
      <view id="bottom-view"></view>
    </scroll-view>

    <!-- 滚动到底部按钮 -->
    <ScrollToBottomButton
      :visible="showScrollToBottomBtn"
      :safe-area-offset="safeBottom"
      @click="scrollToBottomManually"
    />
    <!-- 底部按钮 -->
    <view
      class="chat-input-box flex-left-y"
      :style="{
        bottom: (keyboardHeight ? keyboardHeight : 0) + 'px',
        paddingBottom: (keyboardHeight ? 0 : safeBottom) + 'px',
      }"
    >
      <view class="flex-left btns-wrapper">
        <DeepThinkButton
          :customStyle="{
            'box-shadow': '0px 4px 20px 0px #0000000d',
            border: '1px solid transparent',
          }"
        />
        <SearchButton
          :customStyle="{
            'box-shadow': '0px 4px 20px 0px #0000000d',
            border: '1px solid transparent',
          }"
        />
      </view>
      <view class="input-wrapper flex-left">
        <view class="textarea-wrapper flex-left">
          <textarea
            v-model="chatStore.inputValue"
            class="textarea-input"
            placeholder="请输入法律相关的问题"
            placeholder-class="placeholder-style"
            auto-height
            confirm-type="send"
            :show-confirm-bar="false"
            :maxlength="-1"
            :cursor-spacing="10"
            :fixed="true"
            :adjust-position="false"
          />
        </view>
        <view class="send-btn-wrapper">
          <image
            v-if="!chatStore.isGenerating"
            class="send-btn"
            width="80rpx"
            height="80rpx"
            src="@/static/images/send-btn.svg"
            mode="aspectFit"
            @click="createSSEChat"
          />
          <image
            v-if="chatStore.isGenerating"
            class="send-btn"
            width="80rpx"
            height="80rpx"
            src="@/static/images/answering.svg"
            mode="aspectFit"
            @tap.stop="handleStopChat"
          />
        </view>
      </view>
    </view>
    <!-- 引用内容弹窗 -->
    <ContentPopup
      v-model="popupShow"
      :title="popupTitle"
      :content="popupContent"
      :status="lawStatus"
    />
  </view>
</template>

<script lang="ts" setup>
import CustomNavbar from '@/components/CustomNavbar.vue'
import ContentPopup from './components/ContentPopup.vue'
import ScrollToBottomButton from './components/ScrollToBottomButton.vue'
import ChatMessageList from './components/ChatMessageList.vue'
import DeepThinkButton from '@/components/DeepThinkButton.vue'
import SearchButton from '@/components/SearchButton.vue'
import { useChatStore, useUserStore } from '@/store'
import { getChatMessages } from '@/pages-sub/chat/http/chatApi'
import {
  ChatStatusEnum,
  ChatMessage,
  arrayBufferToString,
  findLastAssistantMessage,
  updateMessageContent,
  processSSEDataWithBuffer,
  handleAssistantMessage,
} from './utils/chatContentUtil'
import { getEnvBaseUrl, throttle, deepCloneArray } from '@/utils'
import { stopChat } from '@/service/index/effective'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

// 获取窗口高度
const windowHeight = ref(0)
const keyboardHeight = ref(0)
const bottomView = ref('bottom-view')
const navbarRef = ref()
const navbarHeight = ref(0) // 默认值
const safeBottom = ref(0) // 安全区域底部高度
const chatStore = useChatStore()
const userStore = useUserStore()
const chatMessagesList = ref<ChatMessage[]>([]) // 聊天消息列表
const popupShow = ref(false)
const popupTitle = ref('')
const lawStatus = ref('')
const popupContent = ref('')
const routeOptions = ref(null) // 保存路由参数
// SSE数据缓冲区，用于处理分片的JSON数据
const sseDataBuffer = ref('')
const currentMessageInfo = ref<{ taskId: string; conversationId: string }>({
  taskId: '',
  conversationId: '',
})
const isStopped = ref(false) // 添加中止状态标志位

// 滚动控制相关变量
const isUserScrolling = ref(false) // 用户是否正在手动滚动
const showScrollToBottomBtn = ref(false) // 是否显示滚动到底部按钮
const isAutoScrollEnabled = ref(true) // 是否启用自动滚动
const isAutoScrolling = ref(false) // 是否正在自动滚动
const lastScrollTop = ref(0) // 上次滚动位置

// ==================== 生命周期函数 ====================

// 监听导航栏高度变化
watch(
  () => navbarRef.value?.totalHeight,
  (newHeight) => {
    if (newHeight) {
      navbarHeight.value = newHeight
    }
  },
  { immediate: true },
)

// 页面显示时获取聊天消息
onShow(() => {})

// 页面卸载时重置生成状态
onUnload(() => {
  chatStore.isGenerating = false
})

// 页面加载时初始化
onLoad((options) => {
  // 初始化系统信息
  initializeSystemInfo()

  // 初始化滚动状态
  initializeScrollState()

  // 设置键盘监听
  setupKeyboardListener()

  getChatMessagesData()
  // 保存路由参数
  routeOptions.value = options
})

// ==================== 工具函数 ====================

// 防抖处理滚动事件
let scrollTimer: number | null = null

// 初始化系统信息和UI状态
const initializeSystemInfo = () => {
  const systemInfo = uni.getSystemInfoSync()
  windowHeight.value = systemInfo.windowHeight
  safeBottom.value = systemInfo.safeAreaInsets?.bottom || 0
}

// 初始化滚动状态
const initializeScrollState = () => {
  isAutoScrollEnabled.value = true
  isUserScrolling.value = false
  showScrollToBottomBtn.value = false
  isAutoScrolling.value = false
  scrollToBottom(100)
}

// 设置键盘监听
const setupKeyboardListener = () => {
  uni.onKeyboardHeightChange((res: any) => {
    keyboardHeight.value = res.height
    if (res.height) scrollToBottom(100)
  })
}

// ==================== 业务逻辑函数 ====================

// 触发响应式更新
const triggerMessageUpdate = (index: number, message: ChatMessage) => {
  chatMessagesList.value.splice(index, 1, { ...message })
}

// 统一处理聊天错误
const handleChatError = (errorText: string) => {
  chatStore.isGenerating = false
  const result = findLastAssistantMessage(chatMessagesList.value)
  if (result) {
    updateMessageContent(result.message, errorText)
    result.message.loading = false
    triggerMessageUpdate(result.index, result.message)
  }
}

// 处理聊天停止
const handleChatStop = () => {
  chatStore.isGenerating = false
  const result = findLastAssistantMessage(chatMessagesList.value)
  if (result) {
    // 追加"(已终止生成)"而不是覆盖内容
    let content = ''
    try {
      // 解析原内容
      const arr = JSON.parse(result.message.chatContent)
      if (Array.isArray(arr) && arr.length > 0 && arr[0].data && arr[0].data.outputs) {
        const answer = arr[0].data.outputs.answer || ''
        arr[0].data.outputs.answer = answer
        content = JSON.stringify(arr)
      } else {
        content = result.message.chatContent + '\n(已终止生成)'
      }
    } catch {
      content = result.message.chatContent + '\n(已终止生成)'
    }
    result.message.messageStatus = 0
    result.message.loading = false
    updateMessageContent(result.message, content, 0)
    triggerMessageUpdate(result.index, result.message)
  }
  // 清空当前消息信息
  currentMessageInfo.value = { taskId: '', conversationId: '' }
}

// 处理SSE消息块追加
const handleMessageChunk = (chunk: string) => {
  const result = findLastAssistantMessage(chatMessagesList.value)
  if (result) {
    // 解析当前 chatContent
    let arr = []
    try {
      arr = JSON.parse(result.message.chatContent)
    } catch (e) {
      arr = [{ data: { outputs: { answer: '' } } }]
    }

    // 检查chunk中是否包含<think>和</think>标签
    if (chunk.includes('<think>')) {
      result.message.thinkStatus = 'answer'
      // 初始化思考内容
      result.message.thoughtAbout = ''
    } else if (chunk.includes('</think>')) {
      // 思考结束 修改status
      result.message.status = ChatStatusEnum.DONE
      result.message.thinkStatus = 'done'
    }

    if (
      result.message.isDeepThinking &&
      !result.message.thinkStatus &&
      !chunk.includes('<think>')
    ) {
      result.message.thinkStatus = 'error'
      result.message.status = ChatStatusEnum.ERROR
    }
    // 如果是思考状态，直接累加chunk到thoughtAbout
    if (result.message.thinkStatus === 'answer') {
      // 处理chunk中的think标签
      let thinkContent = chunk
      thinkContent = thinkContent.replace(/<think>/g, '')
      thinkContent = thinkContent.replace(/<\/think>/g, '')
      result.message.thoughtAbout = (result.message.thoughtAbout || '') + thinkContent
    }

    // 拼接 answer
    if (arr.length > 0 && arr[0].data && arr[0].data.outputs) {
      arr[0].data.outputs.answer = (arr[0].data.outputs.answer || '') + chunk
    }
    updateMessageContent(result.message, JSON.stringify(arr))

    // 确保loading状态为false（开始接收内容）
    if (result.message.loading) {
      result.message.loading = false
    }
    triggerMessageUpdate(result.index, result.message)

    // 每次内容更新后都尝试滚动
    nextTick(() => {
      scrollToBottom(100)
    })
  }
}

// 处理单个完整的SSE消息
const processSSEMessage = (line: string) => {
  const jsonStr = line.substring(5).trim() // 移除 'data:' 前缀
  if (jsonStr) {
    try {
      const dataObj = JSON.parse(jsonStr)
      if (dataObj.event === 'node_started' || dataObj.event === 'workflow_started') {
        if (!currentMessageInfo.value.taskId) {
          currentMessageInfo.value = {
            taskId: dataObj.taskId,
            conversationId: dataObj.conversationId,
          }
        }
        if (dataObj.data) {
          setMsgItemStatus(dataObj)
        }
      } else if (dataObj.event === 'message') {
        const chunk = typeof dataObj.answer === 'string' ? dataObj.answer : ''
        handleMessageChunk(chunk)

        if (chatStore.isDeepThinking && dataObj.data) {
          setMsgItemStatus(dataObj)
        }
        nextTick(() => {
          scrollToBottom(100)
        })
      } else if (dataObj.event === 'message_end') {
        chatStore.isGenerating = false
      } else if (dataObj.event === 'node_finished') {
        const result = findLastAssistantMessage(chatMessagesList.value)
        if (dataObj.data.title === 'law_search') {
          if (result) {
            result.message.lawSearchList = deepCloneArray(
              dataObj.data?.outputs?.json?.[0]?.text || [],
            )
          }
        }
        if (dataObj.data.title === 'article_search') {
          if (result) {
            result.message.articleSearchList = deepCloneArray(
              dataObj.data?.outputs?.json?.[0]?.text || [],
            )
          }
        }
        if (dataObj.data.title === 'web_search') {
          if (result) {
            result.message.webSearchList = deepCloneArray(
              dataObj.data?.outputs?.json?.[0]?.result || [],
            )
          }
        }
        if (dataObj.data.title === 'case_search') {
          if (result) {
            result.message.caseSearchList = deepCloneArray(
              dataObj.data?.outputs?.json?.[0]?.text || [],
            )
          }
        }
        // 处理 node_finished 事件
        // 确保状态更新
        if (dataObj.data) {
          setMsgItemStatus(dataObj)
        }
      } else if (dataObj.event === 'workflow_finished') {
        getLastChatMsgData()
      }
    } catch (err) {
      console.log('err ==> ', err)
    }
  }
}

const handleStopChat = throttle(() => {
  const taskId = currentMessageInfo.value.taskId
  const result = findLastAssistantMessage(chatMessagesList.value)
  if (result) {
    // 先更新状态
    result.message.status = ChatStatusEnum.STOP
    // 立即触发视图更新
    triggerMessageUpdate(result.index, result.message)
    // 设置中止状态
    isStopped.value = true
  }
  stopChat({ taskId }).then((res) => {
    if (res.code === RESPONSE_CODE_SUCCESS) {
      handleChatStop()
    }
  })
})

const createSSEChat = throttle(() => {
  const inputValue = chatStore.inputValue
  if (!inputValue) {
    uni.showToast({ title: '请输入想咨询的问题', icon: 'none' })
    return
  }

  // 重置滚动状态，确保新对话可以自动滚动
  isAutoScrollEnabled.value = true
  isUserScrolling.value = false
  showScrollToBottomBtn.value = false
  isAutoScrolling.value = false

  chatStore.isGenerating = true
  const chatParams = {
    chatAttachInfo: [],
    chatId: chatStore.chatId,
    chatType: '1',
    content: inputValue,
    docTypeCode: '',
    labelId: '',
    modelCode: 'qianwen',
    searchTypes: [],
    thinking: chatStore.isDeepThinking ? 1 : 0,
    webSearch: chatStore.isInternetSearchEnabled ? 1 : 0,
  }
  chatMessagesList.value.push({
    chatRole: 'user',
    chatContent: JSON.stringify({ content: chatParams.content }),
    feedback: '',
    createTime: Date.now(),
    messageId: 'user-' + Date.now(), // 可自定义唯一id
  })
  // 插入 assistant 占位消息
  const loadingMsgId = 'assistant-loading-' + Date.now()
  const loadingMsg = {
    chatRole: 'assistant',
    chatContent: JSON.stringify([{ data: { outputs: { answer: '' } } }]),
    feedback: '',
    createTime: Date.now(),
    messageId: loadingMsgId,
    loading: true,
    status: ChatStatusEnum.PREPARE,
    isDeepThinking: chatStore.isDeepThinking,
    thinkStatus: undefined,
    isThoughtExpanded: true,
    caseSearchList: [],
    lawSearchList: [],
    articleSearchList: [],
  }
  chatMessagesList.value.push(loadingMsg)

  // 强制触发更新
  nextTick(() => {
    // 找到刚创建的消息并触发更新
    const lastIndex = chatMessagesList.value.length - 1
    triggerMessageUpdate(lastIndex, chatMessagesList.value[lastIndex])
  })

  chatStore.inputValue && chatStore.clearInputValue()

  nextTick(() => {
    scrollToBottom(100)
  })
  // 流式输出
  startSSE(chatParams)
})

// 流式输出
const startSSE = (chatParams: any) => {
  const requestTask = wx.request({
    url: getEnvBaseUrl() + '/api/bff-iterms-saas/chat/sseChat',
    method: 'POST',
    enableChunked: true,
    timeout: 6000000,
    responseType: 'arraybuffer',
    header: {
      Csrf_token: userStore.getToken(),
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9',
      Accept: 'text/event-stream',
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no',
      responseType: 'arraybuffer',
    },
    data: chatParams,
    success(res) {
      // 兼容性处理
      chatStore.isGenerating = false // 恢复状态
      if (res.statusCode === 500) {
        handleChatError('[服务器异常，未能获取回复]')
        uni.showToast({
          title: '服务器异常，未能获取回复',
          icon: 'none',
        })
      }
    },
    fail(err) {
      console.log('err ==> ', err)
      handleChatError('[服务器异常，未能获取回复]')
      uni.showToast({
        title: '服务器异常，未能获取回复',
        icon: 'none',
      })
    },
  } as any)
  ;(requestTask as any).onChunkReceived((res) => {
    const text = arrayBufferToString(res.data)
    // 使用新的SSE缓冲处理函数，解决JSON截断问题
    processSSEDataWithBuffer(text, sseDataBuffer, processSSEMessage)
  })
}

const getLastChatMsgData = () => {
  getChatMessages(chatStore.chatId).then((res) => {
    if (res.code === RESPONSE_CODE_SUCCESS && res.data) {
      const historyChat = res.data as ChatMessage[]
      // 处理历史数据中的think状态
      historyChat.forEach((item, index) => {
        if (item.chatRole === 'assistant' && historyChat.length === index + 1) {
          try {
            const chatContent = JSON.parse(item.chatContent)
            const { caseSearchList, lawSearchList, articleSearchList, webSearchList } =
              handleAssistantMessage(chatContent)
            const result = findLastAssistantMessage(chatMessagesList.value)
            if (result) {
              result.message.caseSearchList = deepCloneArray(caseSearchList)
              result.message.lawSearchList = deepCloneArray(lawSearchList)
              result.message.articleSearchList = deepCloneArray(articleSearchList)
              result.message.webSearchList = deepCloneArray(webSearchList)
              triggerMessageUpdate(result.index, result.message)
            }
          } catch (e) {
            console.log('解析历史消息失败:', e)
          }
        }
      })
    }
  })
}

const getChatMessagesData = () => {
  getChatMessages(chatStore.chatId).then((res) => {
    if (res?.data) {
      chatMessagesList.value = res.data as ChatMessage[]

      // 处理历史数据中的think状态
      chatMessagesList.value.forEach((item) => {
        if (item.chatRole === 'assistant') {
          try {
            const chatContent = JSON.parse(item.chatContent)
            let {
              caseSearchList,
              lawSearchList,
              articleSearchList,
              webSearchList,
              completeAnswer,
              thinkInfo,
            } = handleAssistantMessage(chatContent)

            // 使用深拷贝赋值搜索列表
            item.caseSearchList = deepCloneArray(caseSearchList)
            item.lawSearchList = deepCloneArray(lawSearchList)
            item.articleSearchList = deepCloneArray(articleSearchList)
            item.webSearchList = deepCloneArray(webSearchList)

            // 处理think相关状态
            if (thinkInfo.hasThink) {
              item.thinkStatus = 'done'
              item.isThoughtExpanded = true
              item.isDeepThinking = true
              item.status = ChatStatusEnum.DONE
              item.thoughtAbout = thinkInfo.thoughtAbout
            }

            if (item.messageStatus === 0) {
              completeAnswer = '已终止生成'
            }
            // 如果有完整答案，更新消息内容
            if (completeAnswer) {
              updateMessageContent(
                item,
                JSON.stringify([{ data: { outputs: { answer: completeAnswer } } }]),
              )
            }
          } catch (e) {
            console.log('解析历史消息失败:', e)
          }
        }
      })

      scrollToBottom(100)

      // 从主页跳转过来  createChat 对话 然后相当于发起一次对话
      if (routeOptions.value?.type === 'newChat') {
        nextTick(() => {
          createSSEChat()
        })
      }
    }
  })
}

// ==================== 滚动相关函数 ====================

const scrollToBottom = (time = 0) => {
  // 只有在启用自动滚动时才执行滚动
  if (!isAutoScrollEnabled.value) {
    return
  }

  // 设置自动滚动标识
  isAutoScrolling.value = true

  bottomView.value = ''
  setTimeout(() => {
    bottomView.value = 'bottom-view'
    // 滚动完成后重置自动滚动标识
    setTimeout(() => {
      isAutoScrolling.value = false
    }, 200) // 额外延迟确保滚动动画完成
  }, time)
}

// 手动滚动到底部
const scrollToBottomManually = () => {
  // 重新启用自动滚动
  isAutoScrollEnabled.value = true
  isUserScrolling.value = false
  showScrollToBottomBtn.value = false

  // 清除可能存在的滚动定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }

  // 设置自动滚动标识
  isAutoScrolling.value = true

  bottomView.value = ''
  setTimeout(() => {
    bottomView.value = 'bottom-view'
    // 滚动完成后重置自动滚动标识
    setTimeout(() => {
      isAutoScrolling.value = false
    }, 200)
  }, 100)
}

// ==================== 事件处理函数 ====================

const setMsgItemStatus = (dataObj: any) => {
  const data = dataObj.data
  const result = findLastAssistantMessage(chatMessagesList.value)
  let status = result.message.status
  if (data.nodeType === 'llm') {
    if (data.title === 'deepseek-r1') {
      status = ChatStatusEnum.DEEPTHINKING // 深度思考
    }
  } else if (data.nodeType === 'tool') {
    if (data.title === 'law_search') {
      status = ChatStatusEnum.LAW_SEARCHING // 法律检索
    } else if (data.title === 'case_search') {
      status = ChatStatusEnum.CASE_SEARCHING // 案例检索
    } else if (data.title === 'web_search') {
      status = ChatStatusEnum.WEB_SEARCHING // 网页搜索
    } else if (data.title === 'article_search') {
      status = ChatStatusEnum.ARTICLE_SEARCHING // 文章检索
    }
  }
  if (result) {
    if (result.message.status !== ChatStatusEnum.DONE) {
      result.message.status = status
      triggerMessageUpdate(result.index, result.message)
    }
  }
}
// 监听点击事件
const onImgTap = (item: ChatMessage, e: any) => {
  try {
    console.log('item ==> ', item)
    console.log('e ==> ', e)
    // 如果正在生成消息，阻止点击图片引用
    if (chatStore.isGenerating) return
    const popupData = e.id.split('/')
    const type = popupData[0]
    const popupId = popupData[1]
    const showPopContents = item[`${type}SearchList`] || []
    lawStatus.value = ''
    showPopContents.length &&
      showPopContents.forEach((item: any) => {
        if (item.webId === Number(popupId)) {
          popupTitle.value = item.title
          popupContent.value = item.content
          popupShow.value = true
        }
        if (item.lawId === Number(popupId)) {
          popupTitle.value = `${item.lawName}-${item.lawNum}`
          popupContent.value = item.lawContent
          lawStatus.value = item.status
          popupShow.value = true
        }
        if (item.caseNum === Number(popupId)) {
          popupTitle.value = item.title
          popupContent.value = item.format_paragraphs.judge_result.court_believe_words
          popupShow.value = true
        }
        if (item.articleId === Number(popupId)) {
          popupTitle.value = item.document_name
          // 去掉content内的回车和空格
          popupContent.value = item.content.replace(/[\r\n\s]+/g, ' ')
          popupShow.value = true
        }
      })
  } catch (error) {
    console.error('解析法院认定内容失败：', error)
  }
}

const toggleThoughtExpanded = (index: number) => {
  const item = chatMessagesList.value[index]
  if (item) {
    item.isThoughtExpanded = !item.isThoughtExpanded
    triggerMessageUpdate(index, item)
  }
}

// 聊天区域触摸事件处理函数
const handleChatTouchStart = () => {
  // 用户开始触摸聊天区域，停止自动滚动
  isUserScrolling.value = true
  isAutoScrollEnabled.value = false
  isAutoScrolling.value = false // 中断任何正在进行的自动滚动

  // 清除滚动定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }
}

// 滚动事件处理函数
const handleScroll = (e: any) => {
  // 如果正在自动滚动，不处理按钮显示逻辑
  if (isAutoScrolling.value) {
    return
  }

  const scrollTop = e.detail.scrollTop
  const scrollHeight = e.detail.scrollHeight
  const clientHeight = e.detail.height || windowHeight.value - keyboardHeight.value

  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 防抖处理
  scrollTimer = setTimeout(() => {
    // 更精确的底部检测：使用更小的阈值，并检查滚动是否已停止
    const threshold = 5 // 减小阈值提高精度
    const isAtBottom = Math.abs(scrollTop + clientHeight - scrollHeight) <= threshold

    // 如果滚动到底部，重新启用自动滚动并隐藏按钮
    if (isAtBottom) {
      isAutoScrollEnabled.value = true
      showScrollToBottomBtn.value = false
      isUserScrolling.value = false
    } else {
      // 只有在用户主动滚动且不在底部时才显示按钮
      if (isUserScrolling.value) {
        showScrollToBottomBtn.value = true
      }
    }

    lastScrollTop.value = scrollTop
  }, 50) // 50ms 防抖延迟
}
</script>

<style lang="scss" scoped>
.chat-page-container {
  .chat-panel-scroll {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    height: 100vh;
    background: rgb(249, 249, 251);

    .chat-content-wrapper {
      width: 100%;
      padding-bottom: calc(220rpx + constant(safe-area-inset-bottom));
      padding-bottom: calc(220rpx + env(safe-area-inset-bottom));
      // 这里220rpx是输入框的高度
      // 隐藏滚动条
      &::-webkit-scrollbar {
        display: none;
      }
      &.bottom-0 {
        padding-bottom: 220rpx;
      }
    }
  }
}
.chat-input-box {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 220rpx;
  background: rgb(249, 249, 251);
  transition: bottom 0.3s ease-out;
  .btns-wrapper {
    box-sizing: border-box;
    gap: 12rpx;
    width: 100%;
    height: 104rpx;
    padding: 0 24rpx;
  }
  .input-wrapper {
    box-sizing: border-box;
    gap: 12rpx;
    width: 100%;
    min-height: 108rpx;
    padding: 0 24rpx 24rpx 24rpx;
    background: rgb(249, 249, 251);
    .textarea-wrapper {
      flex: 1;
      min-height: 80rpx;
      overflow: hidden;
      background: #fff;
      border-radius: 40rpx;
      box-shadow: 0px 4px 20px 0px #0000000d;

      .textarea-input {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 0 32rpx;
        font-size: 28rpx;
        line-height: 28rpx;
        color: var(--text-color);
        background: #fff;
        border: none;
      }
      :deep(.placeholder-style) {
        font-size: 28rpx;
        color: var(--sub-text-color);
      }
    }
    .send-btn-wrapper {
      flex-shrink: 0;
      width: 80rpx;
      height: 80rpx;
    }
  }
}

.thinking-wrapper {
  box-sizing: border-box;
  display: inline-flex;
  background: #fff;
  border-radius: 24rpx;
  border-top-left-radius: 0;
  .deep-thinking {
    width: 32rpx;
    height: 32rpx;
  }
  .arrow-icon {
    width: 32rpx;
    height: 32rpx;
    transition: transform 0.3s ease;
  }
  .arrowDown {
    transform: rotate(-90deg);
  }
  .arrowRight {
    transform: rotate(90deg);
  }
  .deep-thinking-text {
    margin: 0 16rpx;
    font-size: 28rpx;
    color: var(--text-color);
  }
  .loading-dots {
    width: 20rpx;
    margin-left: 8rpx;
    font-size: 28rpx;
    color: var(--text-color);
    &::after {
      content: '.';
      animation: loadingDots 1.5s infinite;
    }
  }
}

@keyframes loadingDots {
  0% {
    content: '.';
  }
  33% {
    content: '..';
  }
  66% {
    content: '...';
  }
  100% {
    content: '.';
  }
}

// loading 旋转动画
.loading-spinner {
  display: block;
  width: 48rpx;
  height: 48rpx;
  margin-top: 16rpx;
  margin-left: 16rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
