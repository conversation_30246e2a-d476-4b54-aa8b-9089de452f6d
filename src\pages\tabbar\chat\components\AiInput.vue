<template>
  <view class="ai-input-container">
    <view class="input-wrapper">
      <textarea
        ref="textareaRef"
        v-model="chatStore.inputValue"
        class="main-input"
        placeholder="请输入法律相关的问题"
        placeholder-class="placeholder-style"
        auto-height
        :maxlength="500"
        @input="onInput"
        @focus="onFocus"
      />
    </view>

    <view class="button-row">
      <view class="function-buttons">
        <DeepThinkButton />
        <SearchButton />
      </view>
      <view class="send-btn-wrapper" @click="onSend">
        <image
          class="send-btn width-[56rpx] height-[56rpx]"
          src="@/static/images/send-btn.svg"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useChatStore, useUserStore } from '@/store'
import DeepThinkButton from '@/components/DeepThinkButton.vue'
import SearchButton from '@/components/SearchButton.vue'
import { createNewChat } from '@/service/index/effective'
import { throttle } from '@/utils'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const chatStore = useChatStore()
const userStore = useUserStore()
const textareaRef = ref<any>(null)
const emit = defineEmits(['send'])

const onInput = (e: any) => {
  chatStore.updateInputValue(e.detail.value)
}
const onFocus = (e: any) => {
  const value = e.target.value
  if (value.length > 0) {
    chatStore.updateInputValue('')
    nextTick(() => {
      chatStore.updateInputValue(value)
    })
  }
}
const onSend = throttle(async () => {
  try {
    const message = chatStore.inputValue.trim()
    if (!userStore.isLogined) {
      uni.navigateTo({ url: '/pages/login/login' })
      return
    }
    if (!message) {
      uni.showToast({ title: '请输入想咨询的问题', icon: 'none' })
      return
    }
    uni.showLoading({ title: '创建对话中...', mask: true })
    const res: any = await createNewChat({ chatTitle: message.substring(0, 20) })
    if (res.code === RESPONSE_CODE_SUCCESS) {
      chatStore.isGenerating = false
      chatStore.chatId = res.data.id
      uni.hideLoading()
      uni.navigateTo({ url: '/pages-sub/chat/chat-detail?type=newChat' })
    } else if (res.code === '403') {
      uni.hideLoading()
      uni.showToast({
        title: '登录已过期，请重新登录后再试',
        icon: 'none',
        duration: 2000,
      })
    } else {
      uni.hideLoading()
      uni.showToast({
        title: res.msg || '创建对话失败，请稍后重试',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('创建对话失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '网络异常，请检查网络后重试',
      icon: 'none',
      duration: 2000,
    })
  }
})
</script>

<script lang="ts">
export default { name: 'AiInput' }
</script>

<style lang="scss" scoped>
.ai-input-container {
  padding: 24rpx;
  padding-top: 16rpx;
  background-color: #fff;
  border: 2rpx solid #eaebf0;
  border-radius: 24rpx;
  box-shadow: 0px 2px 10px 0px #221d390d;

  .input-wrapper {
    .main-input {
      width: 100%;
      min-height: 110rpx;
      max-height: 200rpx;
      font-size: 28rpx;
      line-height: 1.5;
      text-align: justify;
      resize: none;
      background: transparent;
      border: none;
      outline: none;
      .placeholder-style {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  .button-row {
    @include flex-layout(row, center, space-between);
    height: 56rpx;
    margin-top: 16rpx;
    .function-buttons {
      @include flex-layout(row, center, flex-start);
      gap: 16rpx;
    }

    .send-btn-wrapper {
      flex-shrink: 0;
      width: 56rpx;
      height: 56rpx;
      padding: 10px 0 10px 10px;
      background: #ffffff;
    }
  }
}
</style>
