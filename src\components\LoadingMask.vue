<template>
  <view v-if="visible" class="loading-mask">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
  autoClose?: number // 自动关闭时间(秒)，0表示不自动关闭
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  autoClose: 0,
  text: '加载中...',
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

let autoCloseTimer: number | null = null

// 监听visible变化，处理自动关闭逻辑
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.autoClose > 0) {
      // 清除之前的定时器
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer)
      }
      // 设置新的自动关闭定时器
      autoCloseTimer = setTimeout(() => {
        emit('update:visible', false)
        emit('close')
      }, props.autoClose * 1000)
    } else if (!newVisible && autoCloseTimer) {
      // 如果关闭，清除定时器
      clearTimeout(autoCloseTimer)
      autoCloseTimer = null
    }
  },
)

// 组件卸载时清除定时器
onUnmounted(() => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
  }
})
</script>

<style lang="scss" scoped>
.loading-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(249, 249, 251);

  .loading-content {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 32rpx;
    align-items: center;

    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid #e5e5e5;
      border-top: 6rpx solid #773bef;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
