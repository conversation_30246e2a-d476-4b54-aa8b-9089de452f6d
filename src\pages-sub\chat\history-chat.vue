<route lang="json5" type="page">
{
  style: { navigationStyle: 'custom' },
}
</route>

<template>
  <view class="history-chat-page hide-scrollbar">
    <CustomNavbar
      title="历史对话"
      :is-need-placeholder="true"
      :show-back="true"
      :transparent="false"
    />
    <!-- 空状态 -->
    <view v-if="historyList.length === 0" class="empty-state">
      <view class="empty-wrapper">
        <image src="@/static/images/empty.svg" class="empty-image" />
        <view class="empty-text">暂无数据</view>
      </view>
    </view>
    <!-- 有数据状态 -->
    <view v-else class="history-list-wrapper">
      <view class="history-list-card">
        <view
          v-for="(item, idx) in historyList"
          :key="item.id"
          class="history-list-item"
          @click="goToDetail(item)"
        >
          <text class="item-title">{{ item.chatTitle }}</text>
          <image class="item-arrow" src="@/static/images/arrow-right.svg"></image>
          <view v-if="idx !== historyList.length - 1" class="divider"></view>
        </view>
      </view>
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getHistoryChat } from './http/chatApi'
import { useChatStore } from '@/store'
import CustomNavbar from '@/components/CustomNavbar.vue'

const chatStore = useChatStore()
const currentPageNo = ref(1)
const pageSize = ref(99)
const historyList = ref<any[]>([])

const fetchHistoryList = async () => {
  const res = await getHistoryChat({
    currentPageNo: currentPageNo.value,
    pageSize: pageSize.value,
  })
  // 假设接口返回 { data: [...] }
  if (res && res.data) {
    historyList.value = (res.data as any).list
  } else {
    historyList.value = []
  }
}

onShow(() => {
  fetchHistoryList()
})

const goToDetail = (item: { id: number; chatTitle: string }) => {
  chatStore.chatId = item.id.toString()
  uni.navigateTo({ url: '/pages-sub/chat/chat-detail' })
}
</script>

<style lang="scss" scoped>
.history-chat-page {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  background: #fafbfc;

  // 兼容性更强的滚动条隐藏方案
  -ms-overflow-style: none; /* IE和Edge */
  scrollbar-width: none; /* Firefox */

  // webkit内核浏览器
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }

  // 微信小程序特殊处理
  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 400rpx;
  .empty-wrapper {
    @include flex-layout(column, center, center);
    margin-bottom: 32rpx;
    .empty-image {
      width: 240rpx;
      height: 172rpx;
    }
    .empty-text {
      margin-top: 32rpx;
      font-size: 28rpx;
      color: var(--text-color);
    }
  }
}

// 有数据时的卡片列表
.history-list-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 32rpx;
  .history-list-card {
    display: flex;
    flex-direction: column;
    width: 90vw;
    max-width: 700rpx;
    padding: 0;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 24rpx 0 rgba(185, 175, 255, 0.1);
    &::-webkit-scrollbar {
      display: none;
      width: 0 !important;
      height: 0 !important;
      -webkit-appearance: none;
      background: transparent;
    }
  }
  .history-list-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 32rpx 24rpx;
    .item-title {
      flex: 1;
      font-size: 32rpx;
      line-height: 40rpx;
      color: var(--text-color);
      text-align: left;
      @include text-ellipsis(1);
    }
    .item-arrow {
      width: 24rpx;
      height: 48rpx;
      margin-left: 16rpx;
    }
    .divider {
      position: absolute;
      right: 24rpx;
      bottom: 0;
      left: 24rpx;
      height: 1px;
      content: '';
      background: #f0f0f0;
    }
  }
  .no-more-text {
    display: block;
    width: 100%;
    margin: 32rpx 0 0 0;
    font-size: 24rpx;
    color: #b9b9b9;
    text-align: center;
  }
}
</style>
