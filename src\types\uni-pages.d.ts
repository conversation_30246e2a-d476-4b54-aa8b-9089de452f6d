/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/login/login" |
       "/pages/outUrl/outUrl" |
       "/pages/reviewRecord/reviewRecord" |
       "/pages/scanLogin/scanLogin" |
       "/pages/scanLoginResult/scanLoginResult" |
       "/pages/userCenter/userCenter" |
       "/pages/index/contractReview/contractReview" |
       "/pages/index/contractReview/reviewResult" |
       "/pages/tabbar/chat/index" |
       "/pages-sub/chat/history-chat" |
       "/pages-sub/chat/chat-detail";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/tabbar/chat/index" | "/pages/userCenter/userCenter"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
