<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
    enableShareAppMessage: true,
  },
}
</route>
<template>
  <view class="home-page">
    <image class="bg-image" src="@/static/images/index-bg.svg" mode="aspectFill" />
    <CustomNavbar :show-back="false" :transparent="true" />
    <scroll-view class="scroll" scroll-y>
      <view
        class="overflow-hidden px-4 contract-review-container"
        :style="{ paddingTop: safeAreaInsetsTop + 'px' }"
      >
        <view class="title-container">
          <view class="page-title"><img src="@/static/images/title.svg" alt="" /></view>
          <text class="page-desc">一键开启全新智能合同审查</text>
        </view>
        <view class="upload-container" @click="chooseFile">
          <view class="upload-top">
            <image src="@/static/images/wx-logo.svg" class="wx-image"></image>
            <text class="top-text">从微信文件中上传</text>
          </view>
          <text class="upload-bottom">支持doc/docx/pdf格式</text>
        </view>
        <view class="review-record-container" v-if="reviewRecordList.length > 0">
          <view class="review-record-header">
            <text>合同列表</text>
            <text class="check-more" @click="checkMore">查看更多</text>
          </view>
          <view class="review-record-content">
            <view class="review-record-list">
              <view
                v-for="(item, index) in reviewRecordList"
                :key="index"
                class="review-record-item"
                @click="goToDetail(item)"
              >
                <view class="item-left">
                  <image src="@/static/images/record-item-img.svg" class="item-img"></image>
                  <view class="item-info">
                    <text class="item-title">{{ item.contractName }}</text>
                    <text class="item-status">
                      {{ item.contractStatus }}
                    </text>
                  </view>
                </view>
                <view class="item-right">
                  <image src="@/static/images/arrow-right.svg" class="arrow-right"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <wd-popup
    v-model="uploadingStatusVisible"
    custom-style="border-radius:32rpx; width: 640rpx; height:472rpx;"
  >
    <view class="popup-content-container">
      <image src="@/static/images/uploading-img.svg" alt="" class="upload-status-img" />
      <wd-progress
        :percentage="uploadProgress"
        hide-text
        color="#773BEF"
        class="upload-progress"
      ></wd-progress>
      <text class="status-text">文件上传中，请耐心等待…</text>
      <view class="popup-content-footer">
        <text class="cancel-button cancel-review-button" @click="cancelUpload">取消</text>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import CustomNavbar from '@/components/CustomNavbar.vue'
import { ref, onMounted } from 'vue'
import { useReviewStore, useUserStore } from '@/store'
import { createContract, getContractList } from '@/service/index/effective'
import { getEnvBaseUrl } from '@/utils'
import { onShareAppMessage } from '@dcloudio/uni-app'
import { showToast } from '@/utils/uni-api'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const userStore = useUserStore()
const reviewStore = useReviewStore()

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 3,
  total: 0,
})

const reviewRecordList = ref([])
const uploadingStatusVisible = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('idle') // idle, uploading, completed, canceled, failed
const uploadTask = ref(null)

const navBarHeight = ref(0)
const statusBarHeight = ref(0)
const safeAreaInsetsTop = ref(0)

// 支持的文件扩展名（作为备用验证方式）
const allowedExtensions = ['.pdf', '.doc', '.docx']
const maxFileSize = 100 * 1024 * 1024

// 支持的文件类型
const allowedTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
]

const goToDetail = (item) => {
  if (item.contractStatus === '编辑中') {
    const isDocFile = item.contractName.endsWith('.doc') || item.contractName.endsWith('.docx')
    reviewStore.contractInfo
      .setContractName(item.contractName)
      .setIsDoc(isDocFile)
      .setFileName(item.contractName.split('.')[0])
      .setContractId(item.contractId)
    uni.navigateTo({
      url: `/pages/index/contractReview/contractReview?contractId=${item.contractId}`,
    })
  } else {
    uni.navigateTo({
      url: `/pages/index/contractReview/reviewResult?contractId=${item.contractId}`,
    })
  }
}

const chooseFile = () => {
  if (!userStore.isLogined) {
    uni.navigateTo({ url: '/pages/login/login' })
    return
  }
  uploadStatus.value = 'idle'
  uni.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['.pdf', '.doc', '.docx', 'pdf', 'doc', 'docx'],
    success: (res) => {
      const tempFile = res.tempFiles[0]
      // 检查文件大小
      if (tempFile.size > maxFileSize) {
        showToast(`文件 ${tempFile.name} 超过最大限制 100MB`, 'none', 3000)
        return
      }
      if (!isValidFileType(tempFile.name)) {
        showToast(`文件 ${tempFile.name} 类型不支持`, 'none', 3000)
        return
      }
      uploadFile(tempFile)
    },
    fail: (err) => {
      console.error('选择文件失败:', err)
    },
  })
}

// 验证文件类型
// 验证文件类型 - 通过文件扩展名判断
const isValidFileType = (fileName, type = '') => {
  // 如果type不是'file'且有具体的MIME类型，使用MIME类型判断
  if (type && type !== 'file') {
    return allowedTypes.some((allowedType) => type.includes(allowedType))
  }

  // 否则通过文件扩展名判断
  const fileExt = getFileExtension(fileName).toLowerCase()
  return allowedExtensions.includes(fileExt)
}

const getFileExtension = (fileName) => {
  if (!fileName) return ''
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : ''
}

// 上传单个文件
const uploadFile = async (file) => {
  if (!file || file.status === 'uploading') return

  uploadStatus.value = 'uploading'
  uploadingStatusVisible.value = true

  try {
    uploadTask.value = uni.uploadFile({
      url: `${getEnvBaseUrl()}/api/bff-iterms-saas/attachment/upload-temporary`, // 替换为你的上传接口
      filePath: file.path,
      name: 'file',
      header: {
        Csrf_token: userStore.getToken(),
        Accept: 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
      },
      // formData: {
      //   // 其他需要上传的数据
      //   userId: getUserId(),
      // },
      success: async (res) => {
        if (res.statusCode === 200) {
          const responseData = JSON.parse(res.data)
          uploadStatus.value = 'completed'
          const { templateUrl: contractUrl, templateName: contractName } = responseData.data[0]
          const isDocFile = contractUrl.endsWith('.doc') || contractUrl.endsWith('.docx')
          reviewStore.contractInfo
            .setContractName(file.name)
            .setContractUrl(contractUrl)
            .setIsDoc(isDocFile)
            .setFileName(file.name)
          const createContractRes = await createContract({
            fileName: file.name,
            fileCode: contractUrl,
          })
          if (createContractRes.code !== RESPONSE_CODE_SUCCESS) {
            showToast(createContractRes.message, 'none')
          } else {
            reviewStore.contractInfo.setContractId(
              (createContractRes.data as { contractId: string }).contractId,
            )
            uni.navigateTo({
              url: `/pages/index/contractReview/contractReview?contractId=${(createContractRes.data as { contractId: string }).contractId}`,
            })
          }
        } else {
          uploadStatus.value = 'failed'
          showToast(`上传失败: ${res.statusCode}`, 'none')
        }
      },
      fail: (err) => {
        // 上传失败（网络错误等）
        if (uploadStatus.value !== 'canceled') {
          uploadStatus.value = 'failed'
          showToast('上传失败，请稍后重试', 'none')
          console.error('上传失败:', err)
        }
      },
      complete: () => {
        // 无论成功失败，最后都隐藏对话框（如果是取消状态则在取消函数中已处理）
        if (uploadStatus.value !== 'canceled') {
          setTimeout(() => {
            uploadingStatusVisible.value = false
          }, 500) // 延迟关闭对话框，让用户看到100%的状态
          uploadProgress.value = 0
        }
      },
    })
    uploadTask.value.onProgressUpdate((res) => {
      uploadProgress.value = res.progress
    })
  } catch (error) {
    console.error('上传失败:', error)
    uploadStatus.value = 'failed'
  }
}

// 取消上传
const cancelUpload = () => {
  if (uploadTask.value && uploadStatus.value === 'uploading') {
    uploadTask.value.abort()
    uploadStatus.value = 'canceled'
    uploadingStatusVisible.value = false
  }
}

const checkMore = () => {
  uni.navigateTo({
    url: '/pages/reviewRecord/reviewRecord',
  })
}

// 分享给好友
onShareAppMessage(() => {
  return {
    title: 'iTerms 法律人的合同审查助手', // 分享标题
    path: '/pages/index/index', // 分享路径
    desc: '一键开启全新智能合同审查体验',
    imageUrl: '/static/images/share.png',
  }
})

onLoad(async () => {})

onShow(async () => {
  if (!userStore.isLogined) {
    await userStore.loadUserInfo()
  }
  if (userStore.getToken()) {
    const res = await getContractList(pagination)
    if (res.code !== RESPONSE_CODE_SUCCESS) {
      showToast(res.message || '获取合同列表失败', 'none')
      return
    }
    reviewRecordList.value = (res.data as any)?.list || []
  }
})

onMounted(() => {
  console.log('onMounted')
  // 获取系统信息
  // 从基础库 2.20.1 开始，本接口停止维护，请使用 wx.getSystemSetting、wx.getAppAuthorizeSetting、wx.getDeviceInfo、wx.getWindowInfo、wx.getAppBaseInfo 代替
  const systemInfo = uni.getWindowInfo()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  navBarHeight.value = 44
  safeAreaInsetsTop.value = statusBarHeight.value + navBarHeight.value
})
</script>

<style lang="scss" scoped>
.main-title-color {
  color: #d14328;
}
.home-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  .bg-image {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100vh;
  }
}
.contract-review-container {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 16rpx;
  .title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-top: 120rpx;
    .page-title {
      width: 314rpx;
      height: 80rpx;
    }
    .page-desc {
      margin-top: 8rpx;
      font-size: 32rpx;
      color: #7d7b89;
      letter-spacing: 4rpx;
    }
  }
  .upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 136rpx;
    margin-top: 96rpx;
    color: #ffffff;
    background: linear-gradient(88.45deg, #ad83ff 1.95%, #5d34de 93.85%, #492ed1 138.86%);
    border-radius: 24rpx;
    box-shadow: 0px 4px 20px 0px #221d390d;
    .upload-top {
      display: flex;
      align-items: center;
      .wx-image {
        width: 48rpx;
        height: 48rpx;
      }
      .top-text {
        font-size: 32rpx;
      }
    }
    .upload-bottom {
      font-size: 24rpx;
    }
  }
  .review-record-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 112rpx;
    .review-record-header {
      display: flex;
      text {
        font-size: 24rpx;
        color: #7d7b89;
      }
      .check-more {
        margin-right: 0;
        margin-left: auto;
      }
    }
    .review-record-content {
      padding: 24rpx;
      margin-top: 16rpx;
      border-radius: 16rpx;
      box-shadow: 0 2rpx 8rpx 0 #00000014;
      .review-record-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 2rpx solid #eaebf0;
        .item-left {
          display: flex;
          .item-img {
            width: 96rpx;
            height: 96rpx;
            margin-right: 24rpx;
          }
          .item-info {
            display: flex;
            flex-direction: column;
            .item-title {
              max-width: 500rpx;
              overflow: hidden; /* 隐藏溢出内容 */
              font-size: 32rpx;
              line-height: 48rpx;
              color: #221d39;
              text-overflow: ellipsis; /* 显示省略号 */
              white-space: nowrap; /* 强制单行显示 */
            }
            .item-status {
              margin-top: 8rpx;
              font-size: 24rpx;
              line-height: 36rpx;
              color: #a2a1a9;
            }
          }
        }
        .item-right {
          margin-right: 0;
          margin-left: auto;
          .arrow-right {
            width: 24rpx;
            height: 48rpx;
          }
        }
      }
      .review-record-item:first-of-type {
        padding-top: 0;
      }
      .review-record-item:last-of-type {
        padding-bottom: 0;
        border-bottom: none;
      }
    }
  }
}
.popup-content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  --wot-progress-height: 8rpx;
  .upload-status-img {
    width: 144rpx;
    height: 144rpx;
  }
  :deep(.wd-progress) {
    padding: 0;
    margin-top: 32rpx;
  }
  .status-text {
    margin-top: 32rpx;
    font-size: 32rpx;
    color: #221d39;
  }
  .confirm-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #221d39;
  }
  .popup-content-footer {
    position: fixed;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 112rpx;
    background-color: #fff;
    border-top: 2rpx solid #eaebf0;
    .action-button {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
    }
    .divider {
      width: 2rpx;
      height: 100%;
      background-color: #e9e9e9;
    }
    .cancel-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #464359;
    }
    .cancel-review-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #492ed1;
    }
    .confirm-button {
      font-size: 32rpx;
      font-weight: 500;
      color: #492ed1;
    }
  }
}
</style>
