<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="contract-review-result-page">
    <CustomNavbar
      title="合同审查"
      :show-back="true"
      :transparent="false"
      :useDefaultBack="false"
      @back="backToHome"
    />

    <!-- 合同文件信息 -->
    <view class="review-result-container" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="review-status-icon">
        <image
          v-if="getReviewStatus(contractInfo.progress) === reviewStatusEnum.FAIL"
          src="@/static/images/review-fail.svg"
          class="pdf-icon"
        ></image>
        <image
          v-else-if="getReviewStatus(contractInfo.progress) === reviewStatusEnum.QUEUING"
          src="@/static/images/queuing.svg"
          class="pdf-icon"
        ></image>
        <image
          v-else-if="getReviewStatus(contractInfo.progress) === reviewStatusEnum.REVIEWING"
          src="@/static/images/reviewing.svg"
          class="pdf-icon"
        ></image>
        <image
          v-else-if="getReviewStatus(contractInfo.progress) === reviewStatusEnum.SUCCESS"
          src="@/static/images/review-success.svg"
          class="pdf-icon"
        ></image>
      </view>
      <text class="review-status">
        {{ getReviewStatus(contractInfo.progress) }}
      </text>
      <text v-if="contractInfo.progress >= 100" class="review-tip">
        审查结果请复制以下链接到网页版查看
      </text>
      <text v-else class="review-tip">审查成功后，审查结果请去电脑网页版查看</text>
    </view>

    <!-- 开始审查按钮 -->
    <view class="footer-container">
      <view v-if="contractInfo.progress >= 100" class="footer-content">
        <text class="url-text">ai.iterms.com</text>
        <image src="@/static/images/copy.svg" class="copy-icon" @click="copyLink"></image>
      </view>
      <button v-if="contractInfo.progress < 100" class="back-home-btn" @click="backToHome">
        回到首页
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomNavbar from '@/components/CustomNavbar.vue'
import { queryContractInfoByContractId } from '@/service/index/effective'
import { ContractInfo } from '@/utils/help'
import { getEnvReviewDetailUrl } from '@/utils'
import { useReviewStore } from '@/store'
import { reviewStatusEnum } from '@/store/modules/review/help'
import { showToast } from '@/utils/uni-api'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const reviewStore = useReviewStore()

const navBarHeight = ref(0)
const statusBarHeight = ref(0)
const safeAreaInsetsTop = ref(0)
const contractInfo = ref<ContractInfo>(new ContractInfo())

const getReviewStatus = (progress) => {
  let status = reviewStore.reviewStatus[0].label
  for (const item of reviewStore.reviewStatus) {
    if (progress >= item.value) {
      status = item.label
    }
  }
  return status
}

const backToHome = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

const copyLink = () => {
  uni.setClipboardData({
    data: `${getEnvReviewDetailUrl()}/${contractInfo.value.reviewId}`,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}

// 页面加载
onLoad(async (options) => {
  const contractId = options.contractId
  if (!contractId) {
    showToast({
      title: '页面出现错误，请稍后再试',
      icon: 'none',
    })
    return
  }
  const contractInfoRes = await queryContractInfoByContractId(contractId)
  if (contractInfoRes.code !== RESPONSE_CODE_SUCCESS) {
    showToast({
      title: '获取合同信息失败，请稍后再试',
      icon: 'none',
    })
  } else {
    const data = contractInfoRes.data as any
    const suffix = data.originalFileCode.match(/\.\w+$/)[0]
    const obj = {
      contractUrl: data.originalFileCode,
      contractName: data.contractName,
      isDocFile: suffix !== '.pdf',
      isReviewed: true,
      reviewReordId: data.recordId,
      progress: data.reviewRecordProgress,
    }
    contractInfo.value = new ContractInfo()
      .setContractName(obj.contractName)
      .setContractUrl(obj.contractUrl)
      .setIsDoc(obj.isDocFile)
      .setIsReviewed(obj.isReviewed)
      .setReviewRecordId(obj.reviewReordId)
      .setProgress(obj.progress)
  }
})

// 获取系统信息
onMounted(() => {
  // 从基础库 2.20.1 开始，本接口停止维护，请使用 wx.getSystemSetting、wx.getAppAuthorizeSetting、wx.getDeviceInfo、wx.getWindowInfo、wx.getAppBaseInfo 代替
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  navBarHeight.value = 44
  safeAreaInsetsTop.value = statusBarHeight.value + navBarHeight.value
})
</script>

<style lang="scss" scoped>
.contract-review-result-page {
  min-height: 100vh;
}

// 合同文件信息
.review-result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 176rpx - 182rpx);
  background-color: #fff;
  .review-status-icon {
    width: 296rpx;
    height: 244rpx;
  }
  .review-status {
    margin-top: 48rpx;
    font-size: 32rpx;
    color: #221d39;
  }
  .review-tip {
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #a2a1a9;
  }
}

.footer-container {
  position: fixed;
  bottom: 0;
  display: flex;
  width: 100vw;
  height: 176rpx;
  background-color: #fff;
  .back-home-btn {
    flex: 1;
    height: 88rpx;
    margin: 0 32rpx;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #ffffff;
    background-color: #773bef;
    border: none;
    border-radius: 8rpx;
  }
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: fit-content;
    .url-text {
      font-size: 32rpx;
      color: #7d7b89;
    }
    .copy-icon {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
  }
}
</style>
