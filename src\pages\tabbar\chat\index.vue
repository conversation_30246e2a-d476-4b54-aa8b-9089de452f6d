<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    enableShareAppMessage: true,
  },
}
</route>
<template>
  <view class="home-page">
    <!-- 背景图 -->
    <image class="bg-image" src="@/static/images/index-bg.svg" mode="aspectFill" />
    <!-- 顶部导航 -->
    <CustomNavbar is-need-placeholder :show-back="false" :transparent="true">
      <template #left>
        <image
          class="history-icon"
          src="@/static/images/chat.svg"
          mode="aspectFit"
          @click="handleIconClick"
        />
      </template>
    </CustomNavbar>
    <!-- 主要内容 -->
    <scroll-view class="scroll" scroll-y>
      <image class="chat-slogan" src="@/static/images/chat-slogan.svg" mode="aspectFit" />
      <text
        class="chat-slogan-text line-height-[60rpx] text-[32rpx] tracking-[4rpx] text-center text-[#7D7B89]"
      >
        您身边的AI法律助手
      </text>
      <!-- ai输入框 -->
      <view class="ai-input-wrapper">
        <AiInput />
      </view>
      <!-- 预设问题 -->
      <view class="preset-questions-wrapper">
        <!-- 第一行：两个问题 -->
        <view class="preset-questions-row first-row">
          <view
            v-for="q in presetQuestions.slice(0, 2)"
            :key="q"
            class="preset-question-item flex-left"
            @click="handlePresetQuestion(q)"
          >
            <text class="question-text">{{ q }}</text>
          </view>
        </view>
        <!-- 第二行：一个问题 -->
        <view class="preset-questions-row second-row">
          <view
            v-for="q in presetQuestions.slice(2, 3)"
            :key="q"
            class="preset-question-item flex-left"
            @click="handlePresetQuestion(q)"
          >
            <text class="question-text">{{ q }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavbar from '@/components/CustomNavbar.vue'
import AiInput from './components/AiInput.vue'
import { useUserStore, useChatStore } from '@/store'
import { onShareAppMessage } from '@dcloudio/uni-app'
import { getContractList } from '@/service/index/effective'

defineOptions({
  name: 'Chat',
})

const userStore = useUserStore()
const chatStore = useChatStore()

// 预设问题数据
const presetQuestions = [
  '被打成轻伤，如何向对方索赔？',
  '孕期被辞退，怎么维权?',
  '医疗纠纷中，举证责任是由谁来承担？',
]

const handlePresetQuestion = (question: string) => {
  chatStore.updateInputValue(question)
  uni.vibrateShort({ type: 'light' })
}

onShow(() => {
  chatStore.clearInputValue()
  if (!userStore.isLogined) {
    userStore.loadUserInfo()
  }
})

const handleIconClick = async () => {
  try {
    const res = await getContractList({
      pageNum: 1,
      pageSize: 3,
    })
    uni.navigateTo({ url: '/pages-sub/chat/history-chat' })
  } catch (error) {
    console.log('error', error)
    if (error.statusCode === 403) {
      uni.navigateTo({ url: '/pages/login/login?redirect=/pages-sub/chat/history-chat' })
    }
  }
}

// 分享给好友
onShareAppMessage(() => {
  return {
    title: 'iTerms 法律人的合同审查助手',
    path: '/pages/index/index',
    desc: '一键开启全新智能合同审查体验',
    imageUrl: '/static/images/share.png',
  }
})
</script>

<style lang="scss" scoped>
.home-page {
  position: relative;
  width: 100%;
  min-height: 100vh;

  .bg-image {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100vh;
  }
  .history-icon {
    width: 48rpx;
    height: 48rpx;
    padding: 20rpx 40rpx 0 0;
    margin-top: -20rpx;
    & > image {
      display: block;
      width: 48rpx;
      height: 48rpx;
    }
  }
  .scroll {
    @include flex-layout(column, center, flex-start);
    width: 100vw;
    height: 100%;
    .chat-slogan {
      display: block;
      width: 240rpx;
      height: 90rpx;
      margin: 112rpx auto 0;
    }
    .chat-slogan-text {
      display: block;
      margin: 8rpx auto 0;
    }
    .ai-input-wrapper {
      margin: 96rpx 32rpx 0 32rpx;
    }
    .preset-questions-wrapper {
      display: flex;
      flex-direction: column;
      gap: 24rpx;
      margin: 64rpx 0 0 0;

      .preset-question-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 64rpx;
        padding: 0 20rpx;
        overflow: hidden;
        background-color: #ffffff;
        border: 2rpx solid #eaebf0;
        border-radius: 32rpx;
        &:active {
          background-color: #f8f9fa;
          transform: scale(0.98);
        }

        .question-text {
          display: block;
          width: 100%;
          overflow: hidden;
          font-size: 24rpx;
          color: --text-color;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .preset-questions-row {
        display: flex;

        &.first-row {
          gap: 24rpx;
          margin: 0 64rpx;

          .preset-question-item {
            flex: 1;
          }
        }

        &.second-row {
          justify-content: center;
          margin: 0 152rpx;

          .preset-question-item {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
